# DataFlow Design System

## Overview

The DataFlow design system provides a comprehensive set of design tokens, components, and patterns to ensure consistency across the application. Built with Tailwind CSS and enhanced with custom CSS variables, it supports both light and dark themes (future) and follows WCAG 2.1 AA accessibility guidelines.

## Design Tokens

### Colors

#### Primary Colors (Indigo)
- `--color-primary-50` to `--color-primary-950`: Main brand colors
- Used for: Primary buttons, links, focus states, brand elements

#### Secondary Colors (Gray)
- `--color-secondary-50` to `--color-secondary-900`: Neutral colors
- Used for: Text, backgrounds, borders, subtle elements

#### Accent Colors (Purple)
- `--color-accent-50` to `--color-accent-900`: Accent colors
- Used for: Highlights, gradients, special elements

#### Status Colors
- **Success**: Green palette for positive actions and states
- **Warning**: Yellow/Orange palette for caution states
- **Error**: Red palette for error states and destructive actions
- **Info**: Blue palette for informational content

### Typography

#### Font Families
- **Sans**: Inter (primary), system fallbacks
- **Mono**: JetBrains Mono, Fira Code, Consolas

#### Font Sizes
- `--text-xs`: 12px - Small labels, captions
- `--text-sm`: 14px - Body text, form inputs
- `--text-base`: 16px - Default body text
- `--text-lg`: 18px - Subheadings
- `--text-xl`: 20px - Section headings
- `--text-2xl`: 24px - Page headings
- `--text-3xl`: 30px - Hero headings
- `--text-4xl`: 36px - Large hero text
- `--text-5xl`: 48px - Display text

### Spacing

Based on 4px grid system:
- `--space-1`: 4px
- `--space-2`: 8px
- `--space-3`: 12px
- `--space-4`: 16px (base unit)
- `--space-5`: 20px
- `--space-6`: 24px
- `--space-8`: 32px
- `--space-10`: 40px
- `--space-12`: 48px
- `--space-16`: 64px
- `--space-20`: 80px
- `--space-24`: 96px

### Border Radius
- `--radius-sm`: 2px - Small elements
- `--radius-md`: 6px - Default radius
- `--radius-lg`: 8px - Cards, buttons
- `--radius-xl`: 12px - Large cards
- `--radius-2xl`: 16px - Hero sections
- `--radius-full`: 9999px - Pills, avatars

## Components

### Buttons

#### Variants
- **Primary**: Main call-to-action buttons
- **Secondary**: Secondary actions
- **Success**: Positive actions (save, confirm)
- **Danger**: Destructive actions (delete, cancel)
- **Ghost**: Subtle actions

#### Sizes
- **Small**: Compact buttons for tight spaces
- **Normal**: Default size
- **Large**: Prominent actions

#### Usage
```erb
<%= ui_button "Save Changes", variant: :primary, size: :lg %>
<%= ui_button "Cancel", variant: :secondary %>
<%= ui_button "Delete", variant: :danger, disabled: true %>
```

### Cards

Flexible container component with optional header and footer.

```erb
<%= ui_card title: "Upload Details", subtitle: "Configure your data upload" do %>
  <!-- Card content -->
<% end %>
```

### Badges

Status indicators and labels.

#### Variants
- **Success**: Green - completed, active states
- **Warning**: Yellow - pending, caution states
- **Error**: Red - failed, error states
- **Info**: Blue - informational content
- **Gray**: Neutral - default states

```erb
<%= ui_badge "Completed", variant: :success %>
<%= ui_badge "Processing", variant: :warning %>
```

### Progress Bars

Visual progress indicators with customizable variants.

```erb
<%= ui_progress 75, variant: :primary, show_label: true %>
<%= ui_progress 45, variant: :warning %>
```

### Alerts

Contextual feedback messages.

```erb
<%= ui_alert "Upload successful!", variant: :success, dismissible: true %>
<%= ui_alert "Please check your input", variant: :error %>
```

### Empty States

Placeholder content for empty data states.

```erb
<%= ui_empty_state(
  title: "No uploads yet",
  description: "Get started by uploading your first file",
  icon: upload_icon,
  action: upload_button
) %>
```

### Form Elements

#### Input Fields
- **form-input**: Standard text inputs
- **form-input-error**: Error state styling
- **form-label**: Consistent label styling
- **form-error**: Error message styling
- **form-help**: Help text styling

```erb
<%= form.label :name, class: "form-label" %>
<%= form.text_field :name, class: "form-input" %>
<p class="form-help">Enter a descriptive name</p>
```

## Layout Utilities

### Container
Responsive container with consistent max-width and padding.

```erb
<%= ui_container do %>
  <!-- Content -->
<% end %>
```

### Grid
Responsive grid system for consistent layouts.

```erb
<%= ui_grid do %>
  <!-- Grid items -->
<% end %>
```

## Interactive Components

### File Upload
Enhanced drag-and-drop file upload with progress tracking.

```erb
<div data-controller="file-upload"
     data-file-upload-max-size-value="52428800"
     data-file-upload-accepted-types-value='[".csv", ".xlsx"]'>
  <!-- Upload interface -->
</div>
```

### Mobile Menu
Responsive navigation menu for mobile devices.

```erb
<nav data-controller="mobile-menu">
  <!-- Navigation content -->
</nav>
```

### Dropdown
Accessible dropdown menus with keyboard navigation.

```erb
<div data-controller="dropdown">
  <!-- Dropdown content -->
</div>
```

## Accessibility Features

### Focus Management
- Visible focus indicators on all interactive elements
- Logical tab order throughout the application
- Focus trapping in modals and dropdowns

### Screen Reader Support
- Semantic HTML structure
- ARIA labels and descriptions
- Screen reader announcements for dynamic content

### Color Contrast
- All text meets WCAG AA contrast requirements
- Color is not the only means of conveying information
- High contrast mode support

### Keyboard Navigation
- All functionality accessible via keyboard
- Standard keyboard shortcuts supported
- Skip links for main content areas

## Performance Optimizations

### CSS
- Critical CSS inlined for above-the-fold content
- Non-critical CSS loaded asynchronously
- CSS custom properties for dynamic theming

### JavaScript
- Stimulus controllers loaded on-demand
- Minimal JavaScript for core functionality
- Progressive enhancement approach

### Images and Assets
- Optimized file formats (WebP, AVIF when supported)
- Responsive images with appropriate sizing
- Lazy loading for below-the-fold content

## Browser Support

- **Modern Browsers**: Full feature support
- **Legacy Browsers**: Graceful degradation
- **Mobile Browsers**: Optimized touch interactions
- **Screen Readers**: Full accessibility support

## Usage Guidelines

### Do's
- Use consistent spacing from the design system
- Follow the established color hierarchy
- Implement proper focus management
- Test with keyboard navigation
- Validate with screen readers

### Don'ts
- Don't use hardcoded colors or spacing
- Don't rely solely on color for information
- Don't create custom components without accessibility review
- Don't ignore mobile responsiveness
- Don't skip semantic HTML structure

## Future Enhancements

### Dark Mode
- CSS custom properties ready for theme switching
- Color tokens designed for both light and dark themes
- User preference detection and storage

### Advanced Components
- Data visualization components
- Advanced form controls
- Animation and transition library
- Micro-interaction patterns

### Internationalization
- RTL language support
- Flexible typography for different languages
- Cultural color considerations
