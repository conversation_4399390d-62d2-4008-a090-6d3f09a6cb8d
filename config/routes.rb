Rails.application.routes.draw do
  # Authentication routes
  resource :session
  resources :passwords, param: :token

  # Main application routes
  root "dashboard#index"
  
  resources :data_uploads do
    member do
      patch :process
      get :download_presentation
    end
    
    resources :presentations, only: [:show, :create, :destroy]
  end

  # Background job monitoring (in production, should be protected)
  mount MissionControl::Jobs::Engine, at: "/jobs"

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check

  # PWA files
  get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
end
