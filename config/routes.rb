Rails.application.routes.draw do
  # Billing and subscription routes
  resources :subscriptions, only: [:index, :new, :create, :edit, :update, :destroy]
  resources :plans, only: [:index]
  get '/pricing', to: 'plans#index'
  
  # Stripe webhook
  post '/stripe/webhooks', to: 'stripe_webhooks#create'
  
  # Authentication routes
  resource :session
  resources :passwords, param: :token
  resources :users, only: [:new, :create]

  # Friendly registration routes
  get '/signup', to: 'users#new'
  get '/register', to: 'users#new'
  post '/signup', to: 'users#create'
  post '/register', to: 'users#create'

  # Main application routes
  root "dashboard#index"
  
  resources :data_uploads do
    member do
      patch :process_upload
      get :download_presentation
    end
    
    resources :presentations, only: [:show, :create, :destroy]
  end

  # Background job monitoring (in production, should be protected)
  mount MissionControl::Jobs::Engine, at: "/jobs"

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check

  # PWA files
  get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
end
