Rails.application.configure do
  # Stripe configuration
  Stripe.api_key = Rails.env.production? ? ENV['STRIPE_SECRET_KEY'] : ENV['STRIPE_TEST_SECRET_KEY']
  
  # For development, use test keys
  unless Rails.env.production?
    Stripe.api_key = ENV.fetch('STRIPE_TEST_SECRET_KEY', 'sk_test_dummy_key_for_development')
  end
end

# Stripe webhook endpoint secret
STRIPE_WEBHOOK_SECRET = Rails.env.production? ? ENV['STRIPE_WEBHOOK_SECRET'] : ENV['STRIPE_TEST_WEBHOOK_SECRET']