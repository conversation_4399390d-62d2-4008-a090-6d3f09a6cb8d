default: &default
  dispatchers:
    - polling_interval: 0.5
      batch_size: 500
  workers:
    - queues: "data_processing,ai_processing,presentation_generation"
      threads: 5
      processes: <%= ENV.fetch("JOB_CONCURRENCY", 2) %>
      polling_interval: 0.1
    - queues: "*"
      threads: 3
      processes: 1
      polling_interval: 0.5

development:
  <<: *default

test:
  <<: *default

production:
  <<: *default
