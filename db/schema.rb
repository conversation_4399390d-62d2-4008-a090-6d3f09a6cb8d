# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_08_15_235828) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "data_results", force: :cascade do |t|
    t.bigint "data_upload_id", null: false
    t.text "raw_data"
    t.text "insights"
    t.text "visualization_data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["data_upload_id"], name: "index_data_results_on_data_upload_id"
  end

  create_table "data_uploads", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "name"
    t.string "file_type"
    t.integer "file_size"
    t.string "status"
    t.datetime "processed_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_data_uploads_on_user_id"
  end

  create_table "presentations", force: :cascade do |t|
    t.bigint "data_upload_id", null: false
    t.string "title"
    t.text "slides_data"
    t.datetime "generated_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["data_upload_id"], name: "index_presentations_on_data_upload_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email_address", null: false
    t.string "password_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email_address"], name: "index_users_on_email_address", unique: true
  end

  add_foreign_key "data_results", "data_uploads"
  add_foreign_key "data_uploads", "users"
  add_foreign_key "presentations", "data_uploads"
  add_foreign_key "sessions", "users"
end
