# Create pricing plans
Plan.find_or_create_by(name: 'Free') do |plan|
  plan.stripe_plan_id = 'free_plan'
  plan.price = 0
  plan.monthly_uploads = 5
  plan.features = "5 file uploads per month\nBasic data analysis\nPDF report generation\nEmail support"
end

Plan.find_or_create_by(name: 'Pro') do |plan|
  plan.stripe_plan_id = 'pro_plan'
  plan.price = 29
  plan.monthly_uploads = 100
  plan.features = "100 file uploads per month\nAdvanced AI insights\nCustom report templates\nPriority support\nAPI access"
end

Plan.find_or_create_by(name: 'Enterprise') do |plan|
  plan.stripe_plan_id = 'enterprise_plan'
  plan.price = 99
  plan.monthly_uploads = 1000
  plan.features = "1000 file uploads per month\nWhite-label branding\nDedicated account manager\nCustom integrations\nSLA guarantee"
end

puts "✅ Created #{Plan.count} pricing plans"
puts Plan.all.map { |p| "#{p.name}: #{p.formatted_price} (#{p.monthly_uploads} uploads)" }.join("\n")