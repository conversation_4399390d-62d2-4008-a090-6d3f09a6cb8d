module UiHelper
  # Button component with consistent styling
  def ui_button(text = nil, variant: :primary, size: :normal, type: :button, disabled: false, loading: false, **options, &block)
    content = block_given? ? capture(&block) : text
    
    base_classes = %w[btn]
    base_classes << "btn-#{variant}"
    base_classes << "btn-#{size}" if size != :normal
    base_classes << "opacity-50 cursor-not-allowed" if disabled || loading
    
    options[:class] = class_names(base_classes, options[:class])
    options[:disabled] = true if disabled || loading
    options[:type] = type
    
    if loading
      content = safe_join([
        content_tag(:svg, class: "animate-spin -ml-1 mr-2 h-4 w-4", fill: "none", viewBox: "0 0 24 24") do
          safe_join([
            tag(:circle, cx: "12", cy: "12", r: "10", stroke: "currentColor", "stroke-width": "4", class: "opacity-25"),
            tag(:path, fill: "currentColor", d: "m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z", class: "opacity-75")
          ])
        end,
        content
      ])
    end
    
    button_tag(content, **options)
  end

  # Card component
  def ui_card(title: nil, subtitle: nil, actions: nil, **options, &block)
    options[:class] = class_names("card", options[:class])
    
    content_tag(:div, **options) do
      safe_join([
        (content_tag(:div, class: "card-header") do
          content_tag(:div, class: "flex items-center justify-between") do
            safe_join([
              content_tag(:div) do
                safe_join([
                  (content_tag(:h3, title, class: "text-lg font-medium text-gray-900") if title),
                  (content_tag(:p, subtitle, class: "mt-1 text-sm text-gray-500") if subtitle)
                ].compact)
              end,
              (content_tag(:div, actions, class: "flex items-center space-x-2") if actions)
            ].compact)
          end
        end if title || subtitle || actions),
        content_tag(:div, class: "card-body", &block)
      ].compact)
    end
  end

  # Status badge component
  def ui_badge(text, variant: :gray, **options)
    base_classes = %w[badge]
    base_classes << "badge-#{variant}"
    
    options[:class] = class_names(base_classes, options[:class])
    
    content_tag(:span, text, **options)
  end

  # Progress bar component
  def ui_progress(value, max: 100, variant: :primary, show_label: false, **options)
    percentage = (value.to_f / max.to_f * 100).round(1)
    
    options[:class] = class_names("progress", options[:class])
    
    content_tag(:div, **options) do
      safe_join([
        content_tag(:div, class: "progress-bar progress-bar-#{variant}", style: "width: #{percentage}%"),
        (content_tag(:span, "#{percentage}%", class: "sr-only") unless show_label),
        (content_tag(:div, "#{percentage}%", class: "text-xs text-gray-600 mt-1") if show_label)
      ].compact)
    end
  end

  # Alert/notification component
  def ui_alert(message, variant: :info, dismissible: false, **options)
    variant_classes = {
      success: "bg-green-50 border-green-200 text-green-800",
      warning: "bg-yellow-50 border-yellow-200 text-yellow-800", 
      error: "bg-red-50 border-red-200 text-red-800",
      info: "bg-blue-50 border-blue-200 text-blue-800"
    }
    
    icon_paths = {
      success: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",
      warning: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z",
      error: "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z",
      info: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
    }
    
    base_classes = %w[rounded-md p-4 border]
    base_classes << variant_classes[variant]
    
    options[:class] = class_names(base_classes, options[:class])
    
    content_tag(:div, **options) do
      content_tag(:div, class: "flex") do
        safe_join([
          content_tag(:div, class: "flex-shrink-0") do
            content_tag(:svg, class: "h-5 w-5", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor") do
              tag(:path, "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2", d: icon_paths[variant])
            end
          end,
          content_tag(:div, class: "ml-3") do
            content_tag(:p, message, class: "text-sm font-medium")
          end,
          (content_tag(:div, class: "ml-auto pl-3") do
            content_tag(:div, class: "-mx-1.5 -my-1.5") do
              button_tag(class: "inline-flex rounded-md p-1.5 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600", data: { action: "click->alert#dismiss" }) do
                content_tag(:span, "Dismiss", class: "sr-only") +
                content_tag(:svg, class: "h-5 w-5", viewBox: "0 0 20 20", fill: "currentColor") do
                  tag(:path, "fill-rule": "evenodd", d: "M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z", "clip-rule": "evenodd")
                end
              end
            end
          end if dismissible)
        ].compact)
      end
    end
  end

  # Empty state component
  def ui_empty_state(title:, description:, icon: nil, action: nil, **options)
    options[:class] = class_names("text-center py-12", options[:class])
    
    content_tag(:div, **options) do
      safe_join([
        (content_tag(:div, icon, class: "mx-auto h-12 w-12 text-gray-400 mb-4") if icon),
        content_tag(:h3, title, class: "mt-2 text-sm font-medium text-gray-900"),
        content_tag(:p, description, class: "mt-1 text-sm text-gray-500"),
        (content_tag(:div, action, class: "mt-6") if action)
      ].compact)
    end
  end

  # File type icon helper
  def file_type_icon(file_type, **options)
    options[:class] = class_names("h-8 w-8", options[:class])
    
    case file_type&.downcase
    when 'excel', 'xlsx', 'xls'
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-green-600"))) do
        tag(:path, d: "M4 18h12V6h-4V2H4v16zm6-10h2v2h-2V8zm0 4h2v2h-2v-2z")
      end
    when 'csv'
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-blue-600"))) do
        tag(:path, d: "M4 2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2z")
      end
    when 'json'
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-purple-600"))) do
        tag(:path, d: "M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4z")
      end
    when 'txt', 'text'
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-gray-600"))) do
        tag(:path, d: "M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4z")
      end
    else
      content_tag(:svg, **options.merge(fill: "none", viewBox: "0 0 24 24", stroke: "currentColor", class: class_names(options[:class], "text-gray-400"))) do
        tag(:path, "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2", d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z")
      end
    end
  end

  # Status icon helper
  def status_icon(status, **options)
    options[:class] = class_names("h-5 w-5", options[:class])
    
    case status&.to_s&.downcase
    when 'completed', 'success'
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-green-500"))) do
        tag(:path, "fill-rule": "evenodd", d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z", "clip-rule": "evenodd")
      end
    when 'processing', 'pending'
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-yellow-500 animate-spin"))) do
        tag(:path, "fill-rule": "evenodd", d: "M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z", "clip-rule": "evenodd")
      end
    when 'failed', 'error'
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-red-500"))) do
        tag(:path, "fill-rule": "evenodd", d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z", "clip-rule": "evenodd")
      end
    when 'uploaded'
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-blue-500"))) do
        tag(:path, "fill-rule": "evenodd", d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z", "clip-rule": "evenodd")
      end
    else
      content_tag(:svg, **options.merge(fill: "currentColor", viewBox: "0 0 20 20", class: class_names(options[:class], "text-gray-400"))) do
        tag(:path, "fill-rule": "evenodd", d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z", "clip-rule": "evenodd")
      end
    end
  end

  # Responsive grid helper
  def ui_grid(**options, &block)
    options[:class] = class_names("grid-responsive", options[:class])
    content_tag(:div, **options, &block)
  end

  # Container helper
  def ui_container(**options, &block)
    options[:class] = class_names("container-responsive", options[:class])
    content_tag(:div, **options, &block)
  end
end
