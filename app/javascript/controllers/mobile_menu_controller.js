import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button", "menu", "openIcon", "closeIcon"]

  connect() {
    this.isOpen = false
  }

  toggle() {
    this.isOpen = !this.isOpen
    this.updateMenu()
  }

  close() {
    this.isOpen = false
    this.updateMenu()
  }

  updateMenu() {
    if (this.isOpen) {
      this.menuTarget.classList.remove('hidden')
      this.openIconTarget.classList.add('hidden')
      this.closeIconTarget.classList.remove('hidden')
      this.buttonTarget.setAttribute('aria-expanded', 'true')
    } else {
      this.menuTarget.classList.add('hidden')
      this.openIconTarget.classList.remove('hidden')
      this.closeIconTarget.classList.add('hidden')
      this.buttonTarget.setAttribute('aria-expanded', 'false')
    }
  }

  // Close menu when clicking outside
  disconnect() {
    document.removeEventListener('click', this.closeOnOutsideClick)
  }

  closeOnOutsideClick = (event) => {
    if (!this.element.contains(event.target)) {
      this.close()
    }
  }
}
