import { Controller } from "@hotwired/stimulus"
import { createConsumer } from "@rails/actioncable"

export default class extends Controller {
  static targets = ["status", "progress", "progressBar", "progressMessage", "progressPercent", "statusContainer"]
  static values = { uploadId: Number, currentStatus: String }

  connect() {
    this.consumer = createConsumer()
    this.subscription = null
    this.subscribeToUpdates()
  }

  disconnect() {
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
    if (this.consumer) {
      this.consumer.disconnect()
    }
  }

  subscribeToUpdates() {
    if (!this.uploadIdValue) return

    this.subscription = this.consumer.subscriptions.create(
      {
        channel: "ProcessingStatusChannel",
        upload_id: this.uploadIdValue
      },
      {
        connected: () => {
          console.log(`Connected to processing updates for upload ${this.uploadIdValue}`)
        },

        disconnected: () => {
          console.log(`Disconnected from processing updates for upload ${this.uploadIdValue}`)
        },

        received: (data) => {
          this.handleUpdate(data)
        }
      }
    )
  }

  handleUpdate(data) {
    switch (data.type) {
      case 'status_update':
        this.updateStatus(data.status, data.message)
        break
      case 'progress_update':
        this.updateProgress(data.progress, data.message)
        break
      case 'processing_complete':
        this.handleProcessingComplete(data)
        break
      case 'processing_failed':
        this.handleProcessingFailed(data)
        break
      default:
        console.log('Unknown update type:', data.type)
    }
  }

  updateStatus(status, message) {
    if (this.hasStatusTarget) {
      // Update status badge
      this.statusTarget.className = this.getStatusClasses(status)
      this.statusTarget.innerHTML = this.getStatusIcon(status) + this.getStatusText(status)
    }

    if (this.hasProgressMessageTarget && message) {
      this.progressMessageTarget.textContent = message
    }
  }

  updateProgress(progress, message) {
    if (this.hasProgressBarTarget) {
      this.progressBarTarget.style.width = `${progress}%`
      this.progressBarTarget.setAttribute('aria-valuenow', progress)
    }

    if (this.hasProgressPercentTarget) {
      this.progressPercentTarget.textContent = `${progress}%`
    }

    if (this.hasProgressMessageTarget && message) {
      this.progressMessageTarget.textContent = message
    }

    // Show progress container if hidden
    if (this.hasProgressTarget) {
      this.progressTarget.classList.remove('hidden')
    }
  }

  handleProcessingComplete(data) {
    this.updateStatus('completed', 'Processing completed successfully!')
    
    // Hide progress bar
    if (this.hasProgressTarget) {
      this.progressTarget.classList.add('hidden')
    }

    // Show success message and reload page after delay
    this.showSuccessMessage()
    setTimeout(() => {
      window.location.reload()
    }, 2000)
  }

  handleProcessingFailed(data) {
    this.updateStatus('failed', data.error || 'Processing failed')
    
    // Hide progress bar
    if (this.hasProgressTarget) {
      this.progressTarget.classList.add('hidden')
    }

    // Show error message
    this.showErrorMessage(data.error)
  }

  getStatusClasses(status) {
    const baseClasses = "inline-flex items-center px-3 py-2 rounded-full text-sm font-medium"
    
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-green-100 text-green-800`
      case 'processing':
        return `${baseClasses} bg-yellow-100 text-yellow-800`
      case 'failed':
        return `${baseClasses} bg-red-100 text-red-800`
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`
    }
  }

  getStatusIcon(status) {
    switch (status) {
      case 'completed':
        return `
          <svg class="-ml-1 mr-2 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        `
      case 'processing':
        return `
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
        `
      case 'failed':
        return `
          <svg class="-ml-1 mr-2 h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        `
      default:
        return `
          <svg class="-ml-1 mr-2 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd" />
          </svg>
        `
    }
  }

  getStatusText(status) {
    switch (status) {
      case 'completed':
        return 'Completed'
      case 'processing':
        return 'Processing...'
      case 'failed':
        return 'Failed'
      default:
        return 'Uploaded'
    }
  }

  showSuccessMessage() {
    this.showNotification('Processing completed! Insights and presentations have been generated.', 'success')
  }

  showErrorMessage(error) {
    this.showNotification(`Processing failed: ${error}`, 'error')
  }

  showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden z-50`
    
    const bgColor = type === 'success' ? 'bg-green-50' : 'bg-red-50'
    const textColor = type === 'success' ? 'text-green-800' : 'text-red-800'
    const iconColor = type === 'success' ? 'text-green-400' : 'text-red-400'
    
    const icon = type === 'success' 
      ? `<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />`
      : `<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />`

    notification.innerHTML = `
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 ${iconColor}" fill="currentColor" viewBox="0 0 20 20">
              ${icon}
            </svg>
          </div>
          <div class="ml-3 w-0 flex-1">
            <p class="text-sm font-medium ${textColor}">
              ${message}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button class="rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()">
              <span class="sr-only">Close</span>
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    `

    // Add to page
    document.body.appendChild(notification)

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 5000)
  }
}