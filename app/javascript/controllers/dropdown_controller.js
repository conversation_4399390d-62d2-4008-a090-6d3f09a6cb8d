import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button", "menu"]

  connect() {
    this.isOpen = false
    document.addEventListener('click', this.closeOnOutsideClick)
  }

  disconnect() {
    document.removeEventListener('click', this.closeOnOutsideClick)
  }

  toggle(event) {
    event.preventDefault()
    event.stopPropagation()
    
    this.isOpen = !this.isOpen
    this.updateMenu()
  }

  close() {
    this.isOpen = false
    this.updateMenu()
  }

  updateMenu() {
    if (this.isOpen) {
      this.menuTarget.classList.remove('hidden')
      this.menuTarget.classList.add('animate-fade-in')
      this.buttonTarget.setAttribute('aria-expanded', 'true')
    } else {
      this.menuTarget.classList.add('hidden')
      this.menuTarget.classList.remove('animate-fade-in')
      this.buttonTarget.setAttribute('aria-expanded', 'false')
    }
  }

  closeOnOutsideClick = (event) => {
    if (!this.element.contains(event.target)) {
      this.close()
    }
  }
}
