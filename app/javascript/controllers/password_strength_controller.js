import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["password", "confirmation", "indicator", "bar", "text", "requirement", "match", "matchText", "submit"]

  connect() {
    this.checkStrength()
  }

  checkStrength() {
    const password = this.passwordTarget.value
    const strength = this.calculateStrength(password)
    
    if (password.length > 0) {
      this.showIndicator()
      this.updateStrengthBar(strength)
      this.updateRequirements(password)
    } else {
      this.hideIndicator()
    }
    
    this.checkMatch()
  }

  checkMatch() {
    if (!this.hasConfirmationTarget) return
    
    const password = this.passwordTarget.value
    const confirmation = this.confirmationTarget.value
    
    if (confirmation.length > 0) {
      this.showMatch()
      if (password === confirmation) {
        this.matchTextTarget.textContent = "Passwords match ✓"
        this.matchTextTarget.className = "text-xs text-green-600"
      } else {
        this.matchTextTarget.textContent = "Passwords don't match"
        this.matchTextTarget.className = "text-xs text-red-600"
      }
    } else {
      this.hideMatch()
    }
  }

  calculateStrength(password) {
    let score = 0
    
    // Length check
    if (password.length >= 8) score += 1
    if (password.length >= 12) score += 1
    
    // Character type checks
    if (/[a-z]/.test(password)) score += 1
    if (/[A-Z]/.test(password)) score += 1
    if (/\d/.test(password)) score += 1
    if (/[@$!%*?&]/.test(password)) score += 1
    
    return Math.min(score, 5)
  }

  updateStrengthBar(strength) {
    const percentage = (strength / 5) * 100
    let color = 'bg-red-500'
    let text = 'Very Weak'
    
    if (strength >= 4) {
      color = 'bg-green-500'
      text = 'Strong'
    } else if (strength >= 3) {
      color = 'bg-yellow-500'
      text = 'Fair'
    } else if (strength >= 2) {
      color = 'bg-orange-500'
      text = 'Weak'
    }
    
    this.barTarget.style.width = `${percentage}%`
    this.barTarget.className = `h-1.5 rounded-full transition-all duration-300 ${color}`
    this.textTarget.textContent = text
    this.textTarget.className = `text-xs font-medium ${color.replace('bg-', 'text-')}`
  }

  updateRequirements(password) {
    const requirements = [
      { name: 'length', test: password.length >= 8 },
      { name: 'lowercase', test: /[a-z]/.test(password) },
      { name: 'uppercase', test: /[A-Z]/.test(password) },
      { name: 'digit', test: /\d/.test(password) },
      { name: 'special', test: /[@$!%*?&]/.test(password) }
    ]

    if (this.hasRequirementTargets) {
      this.requirementTargets.forEach(target => {
        const requirement = requirements.find(req => req.name === target.dataset.requirement)
        if (requirement) {
          const svg = target.querySelector('svg')
          if (requirement.test) {
            svg.className = 'h-3 w-3 mr-1 text-green-500'
            target.parentElement.className = 'flex items-center text-green-600'
          } else {
            svg.className = 'h-3 w-3 mr-1 text-gray-400'
            target.parentElement.className = 'flex items-center text-gray-500'
          }
        }
      })
    }
  }

  showIndicator() {
    if (this.hasIndicatorTarget) {
      this.indicatorTarget.style.display = 'block'
    }
  }

  hideIndicator() {
    if (this.hasIndicatorTarget) {
      this.indicatorTarget.style.display = 'none'
    }
  }

  showMatch() {
    if (this.hasMatchTarget) {
      this.matchTarget.style.display = 'block'
    }
  }

  hideMatch() {
    if (this.hasMatchTarget) {
      this.matchTarget.style.display = 'none'
    }
  }
}
