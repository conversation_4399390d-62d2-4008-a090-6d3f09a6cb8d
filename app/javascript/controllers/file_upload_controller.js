import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["dropzone", "input", "preview", "filename", "filesize", "submit"]

  connect() {
    this.setupDragAndDrop()
  }

  setupDragAndDrop() {
    const dropzone = this.dropzoneTarget

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      dropzone.addEventListener(eventName, this.preventDefaults, false)
      document.body.addEventListener(eventName, this.preventDefaults, false)
    })

    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
      dropzone.addEventListener(eventName, () => this.highlight(), false)
    })

    ['dragleave', 'drop'].forEach(eventName => {
      dropzone.addEventListener(eventName, () => this.unhighlight(), false)
    })

    // Handle dropped files
    dropzone.addEventListener('drop', this.handleDrop.bind(this), false)
  }

  preventDefaults(e) {
    e.preventDefault()
    e.stopPropagation()
  }

  highlight() {
    this.dropzoneTarget.classList.add('border-indigo-500', 'border-solid', 'bg-indigo-50')
    this.dropzoneTarget.classList.remove('border-gray-300', 'border-dashed')
  }

  unhighlight() {
    this.dropzoneTarget.classList.remove('border-indigo-500', 'border-solid', 'bg-indigo-50')
    this.dropzoneTarget.classList.add('border-gray-300', 'border-dashed')
  }

  handleDrop(e) {
    const dt = e.dataTransfer
    const files = dt.files

    if (files.length > 0) {
      this.handleFiles(files[0]) // Only handle first file
    }
  }

  fileSelected(e) {
    const file = e.target.files[0]
    if (file) {
      this.handleFiles(file)
    }
  }

  handleFiles(file) {
    // Validate file type
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
      'text/plain',
      'application/json'
    ]

    if (!allowedTypes.includes(file.type)) {
      this.showError(`File type "${file.type}" is not supported. Please upload Excel, CSV, TXT, or JSON files.`)
      return
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024 // 50MB in bytes
    if (file.size > maxSize) {
      this.showError(`File size too large. Maximum size is 50MB.`)
      return
    }

    // Show file preview
    this.showFilePreview(file)
    
    // Enable submit button
    this.enableSubmit()
  }

  showFilePreview(file) {
    this.filenameTarget.textContent = file.name
    this.filesizeTarget.textContent = this.formatFileSize(file.size)
    this.previewTarget.classList.remove('hidden')
  }

  enableSubmit() {
    if (this.hasSubmitTarget) {
      this.submitTarget.disabled = false
      this.submitTarget.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed')
    }
  }

  showError(message) {
    // Create or update error message
    let errorDiv = this.element.querySelector('.file-upload-error')
    
    if (!errorDiv) {
      errorDiv = document.createElement('div')
      errorDiv.className = 'file-upload-error bg-red-50 border border-red-200 rounded-md p-3 mt-3'
      errorDiv.innerHTML = `
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-800 error-message"></p>
          </div>
        </div>
      `
      this.dropzoneTarget.parentNode.appendChild(errorDiv)
    }
    
    errorDiv.querySelector('.error-message').textContent = message
    
    // Auto-hide error after 5 seconds
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.parentNode.removeChild(errorDiv)
      }
    }, 5000)
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}