class Presentation < ApplicationRecord
  belongs_to :data_upload
  has_one_attached :pdf_file

  validates :title, presence: true

  scope :recent, -> { order(generated_at: :desc) }

  def parsed_slides_data
    @parsed_slides_data ||= JSON.parse(slides_data) if slides_data.present?
  rescue JSON::ParserError
    []
  end

  def slide_count
    parsed_slides_data.length
  end

  def generated?
    generated_at.present?
  end

  def pdf_attached?
    pdf_file.attached?
  end

  # Generate a safe filename for download
  def filename
    "#{title.parameterize}-#{id}.pdf"
  end
end
