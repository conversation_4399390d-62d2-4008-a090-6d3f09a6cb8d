class Subscription < ApplicationRecord
  belongs_to :user
  belongs_to :plan

  validates :stripe_subscription_id, presence: true, uniqueness: true
  validates :status, presence: true, inclusion: { in: %w[active canceled past_due trialing unpaid] }

  enum :status, { 
    active: "active", 
    canceled: "canceled", 
    past_due: "past_due", 
    trialing: "trialing", 
    unpaid: "unpaid" 
  }

  scope :active_subscriptions, -> { where(status: ['active', 'trialing']) }
  scope :current, -> { where('current_period_end > ?', Time.current) }

  def active?
    ['active', 'trialing'].include?(status) && current_period_end > Time.current
  end

  def days_until_renewal
    return 0 unless current_period_end
    ((current_period_end - Time.current) / 1.day).ceil
  end

  def usage_percentage(current_uploads)
    return 0 if plan.monthly_uploads == 0
    (current_uploads.to_f / plan.monthly_uploads * 100).round(1)
  end
end
