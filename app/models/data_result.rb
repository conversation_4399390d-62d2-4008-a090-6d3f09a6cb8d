class DataResult < ApplicationRecord
  belongs_to :data_upload

  validates :raw_data, presence: true

  # Parse JSON data safely
  def parsed_data
    @parsed_data ||= JSON.parse(raw_data) if raw_data.present?
  rescue JSON::<PERSON><PERSON><PERSON><PERSON><PERSON>r
    {}
  end

  def parsed_insights
    @parsed_insights ||= JSON.parse(insights) if insights.present?
  rescue JSON::Parser<PERSON>rror
    {}
  end

  def parsed_visualization_data
    @parsed_visualization_data ||= JSON.parse(visualization_data) if visualization_data.present?
  rescue JSON::ParserError
    {}
  end

  # Helper methods for data analysis
  def row_count
    parsed_data.dig('summary', 'row_count') || 0
  end

  def column_count
    parsed_data.dig('summary', 'column_count') || 0
  end

  def has_insights?
    insights.present?
  end

  def has_visualizations?
    visualization_data.present?
  end
end
