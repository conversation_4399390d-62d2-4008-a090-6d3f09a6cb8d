class Plan < ApplicationRecord
  has_many :subscriptions, dependent: :destroy
  has_many :users, through: :subscriptions

  validates :name, presence: true, uniqueness: true
  validates :stripe_plan_id, presence: true, uniqueness: true
  validates :price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :monthly_uploads, presence: true, numericality: { greater_than: 0 }

  scope :active, -> { where.not(stripe_plan_id: nil) }
  scope :by_price, -> { order(:price) }

  def free_plan?
    price == 0
  end

  def features_list
    features.present? ? features.split("\n") : []
  end

  def formatted_price
    return "Free" if free_plan?
    "$#{price.to_i}/month"
  end

  def upload_limit_text
    "#{monthly_uploads} uploads per month"
  end
end
