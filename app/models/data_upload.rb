class DataUpload < ApplicationRecord
  belongs_to :user
  has_one :data_result, dependent: :destroy
  has_many :presentations, dependent: :destroy
  has_one_attached :file

  validates :name, presence: true
  validates :file_type, inclusion: { 
    in: %w[application/vnd.ms-excel 
           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet 
           text/csv 
           text/plain 
           application/json],
    message: "File type not supported"
  }
  validates :status, inclusion: { 
    in: %w[uploaded processing completed failed],
    message: "Invalid status"
  }

  enum :status, {
    uploaded: 'uploaded',
    processing: 'processing', 
    completed: 'completed',
    failed: 'failed'
  }

  scope :recent, -> { order(created_at: :desc) }
  scope :by_status, ->(status) { where(status: status) }

  def excel_file?
    %w[application/vnd.ms-excel application/vnd.openxmlformats-officedocument.spreadsheetml.sheet].include?(file_type)
  end

  def csv_file?
    file_type == 'text/csv'
  end

  def text_file?
    file_type == 'text/plain'
  end

  def json_file?
    file_type == 'application/json'
  end

  def file_extension
    case file_type
    when 'application/vnd.ms-excel' then '.xls'
    when 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' then '.xlsx'
    when 'text/csv' then '.csv'
    when 'text/plain' then '.txt'
    when 'application/json' then '.json'
    else ''
    end
  end
end
