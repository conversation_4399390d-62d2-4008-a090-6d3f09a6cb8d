class User < ApplicationRecord
  has_secure_password
  has_many :sessions, dependent: :destroy
  has_many :data_uploads, dependent: :destroy
  has_many :data_results, through: :data_uploads
  has_many :presentations, through: :data_uploads

  normalizes :email_address, with: ->(e) { e.strip.downcase }

  validates :email_address, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :password, length: { minimum: 8 }, if: -> { new_record? || !password.nil? }
  validates :password, format: {
    with: /\A(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: "must include at least one lowercase letter, one uppercase letter, one digit, and one special character"
  }, if: -> { new_record? || !password.nil? }

  # Virtual attribute for terms acceptance during registration
  attr_accessor :terms_accepted
  validates :terms_accepted, acceptance: { message: "You must accept the terms of service" }, on: :create

  # Helper methods for dashboard stats
  def uploads_count
    data_uploads.count
  end

  def recent_uploads(limit = 5)
    data_uploads.recent.limit(limit)
  end

  def processing_uploads_count
    data_uploads.processing.count
  end

  def completed_uploads_count
    data_uploads.completed.count
  end
end
