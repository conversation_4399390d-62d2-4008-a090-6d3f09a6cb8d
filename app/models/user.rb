class User < ApplicationRecord
  has_secure_password
  has_many :sessions, dependent: :destroy
  has_many :data_uploads, dependent: :destroy
  has_many :data_results, through: :data_uploads
  has_many :presentations, through: :data_uploads

  normalizes :email_address, with: ->(e) { e.strip.downcase }

  validates :email_address, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }

  # Helper methods for dashboard stats
  def uploads_count
    data_uploads.count
  end

  def recent_uploads(limit = 5)
    data_uploads.recent.limit(limit)
  end

  def processing_uploads_count
    data_uploads.processing.count
  end

  def completed_uploads_count
    data_uploads.completed.count
  end
end
