class User < ApplicationRecord
  has_secure_password
  has_many :sessions, dependent: :destroy
  has_many :data_uploads, dependent: :destroy
  has_many :data_results, through: :data_uploads
  has_many :presentations, through: :data_uploads
  has_many :subscriptions, dependent: :destroy

  normalizes :email_address, with: ->(e) { e.strip.downcase }

  validates :email_address, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :password, length: { minimum: 8 }, if: -> { new_record? || !password.nil? }
  validates :password, format: {
    with: /\A(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: "must include at least one lowercase letter, one uppercase letter, one digit, and one special character"
  }, if: -> { new_record? || !password.nil? }

  # Virtual attribute for terms acceptance during registration
  attr_accessor :terms_accepted
  validates :terms_accepted, acceptance: { message: "You must accept the terms of service" }, on: :create

  # Helper methods for dashboard stats
  def uploads_count
    data_uploads.count
  end

  def recent_uploads(limit = 5)
    data_uploads.recent.limit(limit)
  end

  def processing_uploads_count
    data_uploads.processing.count
  end

  def completed_uploads_count
    data_uploads.completed.count
  end

  # Subscription methods
  def current_subscription
    subscriptions.active_subscriptions.current.first
  end

  def current_plan
    current_subscription&.plan || Plan.find_by(name: 'Free')
  end

  def can_upload_more?
    return true unless current_plan # Allow if no plan restrictions
    monthly_uploads_count < current_plan.monthly_uploads
  end

  def monthly_uploads_count
    data_uploads.where(created_at: 1.month.ago..Time.current).count
  end

  def uploads_remaining
    return Float::INFINITY unless current_plan
    [current_plan.monthly_uploads - monthly_uploads_count, 0].max
  end

  def subscription_status
    current_subscription&.status || 'free'
  end

  def stripe_customer
    return nil unless stripe_customer_id
    @stripe_customer ||= Stripe::Customer.retrieve(stripe_customer_id)
  end

  def create_stripe_customer!
    return stripe_customer if stripe_customer_id

    customer = Stripe::Customer.create(
      email: email_address,
      name: "#{email_address}",
      metadata: { user_id: id }
    )
    
    update!(stripe_customer_id: customer.id)
    customer
  end
end
