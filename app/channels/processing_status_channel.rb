class ProcessingStatusChannel < ApplicationCable::Channel
  def subscribed
    upload_id = params[:upload_id]
    
    # Verify the user has access to this upload
    data_upload = Current.user.data_uploads.find_by(id: upload_id)
    
    if data_upload
      stream_for data_upload
      Rails.logger.info "User #{Current.user.id} subscribed to processing updates for upload #{upload_id}"
    else
      reject
      Rails.logger.warn "User #{Current.user.id} attempted to subscribe to unauthorized upload #{upload_id}"
    end
  end

  def unsubscribed
    Rails.logger.info "User #{Current.user&.id} unsubscribed from processing updates"
  end
end