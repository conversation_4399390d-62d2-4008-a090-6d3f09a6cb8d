<% content_for :title, "Sign Up - DataFlow" %>

<div class="min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <div class="flex justify-center">
      <div class="h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center">
        <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      </div>
    </div>
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
      Create your account
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600">
      Or
      <%= link_to "sign in to your existing account", new_session_path, 
          class: "font-medium text-indigo-600 hover:text-indigo-500" %>
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <%= form_with model: @user, local: true, 
          class: "space-y-6", 
          data: { controller: "password-strength" } do |form| %>
        
        <!-- Display validation errors -->
        <% if @user.errors.any? %>
          <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Please correct the following errors:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Email Address -->
        <div>
          <%= form.label :email_address, "Email address", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.email_field :email_address, 
                required: true, 
                autofocus: true, 
                autocomplete: "email",
                placeholder: "Enter your email address",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm #{'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' if @user.errors[:email_address].any?}",
                aria: { describedby: @user.errors[:email_address].any? ? "email-error" : nil } %>
          </div>
          <% if @user.errors[:email_address].any? %>
            <p class="mt-2 text-sm text-red-600" id="email-error">
              <%= @user.errors[:email_address].first %>
            </p>
          <% end %>
        </div>

        <!-- Password -->
        <div>
          <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.password_field :password, 
                required: true, 
                autocomplete: "new-password",
                placeholder: "Create a strong password",
                maxlength: 72,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm #{'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' if @user.errors[:password].any?}",
                data: { "password-strength-target": "password", action: "input->password-strength#checkStrength" },
                aria: { describedby: "password-requirements #{'password-error' if @user.errors[:password].any?}" } %>
          </div>
          
          <!-- Password strength indicator -->
          <div class="mt-2" data-password-strength-target="indicator" style="display: none;">
            <div class="flex items-center space-x-2">
              <div class="flex-1 bg-gray-200 rounded-full h-2">
                <div class="h-2 rounded-full transition-all duration-300" data-password-strength-target="bar"></div>
              </div>
              <span class="text-xs font-medium" data-password-strength-target="text"></span>
            </div>
          </div>
          
          <!-- Password requirements -->
          <div class="mt-2 text-sm text-gray-600" id="password-requirements">
            <p class="font-medium">Password must contain:</p>
            <ul class="mt-1 space-y-1 text-xs">
              <li class="flex items-center">
                <span data-password-strength-target="requirement" data-requirement="length">
                  <svg class="h-3 w-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </span>
                At least 8 characters
              </li>
              <li class="flex items-center">
                <span data-password-strength-target="requirement" data-requirement="lowercase">
                  <svg class="h-3 w-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </span>
                One lowercase letter
              </li>
              <li class="flex items-center">
                <span data-password-strength-target="requirement" data-requirement="uppercase">
                  <svg class="h-3 w-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </span>
                One uppercase letter
              </li>
              <li class="flex items-center">
                <span data-password-strength-target="requirement" data-requirement="digit">
                  <svg class="h-3 w-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </span>
                One number
              </li>
              <li class="flex items-center">
                <span data-password-strength-target="requirement" data-requirement="special">
                  <svg class="h-3 w-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </span>
                One special character (@$!%*?&)
              </li>
            </ul>
          </div>
          
          <% if @user.errors[:password].any? %>
            <p class="mt-2 text-sm text-red-600" id="password-error">
              <%= @user.errors[:password].first %>
            </p>
          <% end %>
        </div>

        <!-- Password Confirmation -->
        <div>
          <%= form.label :password_confirmation, "Confirm password", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.password_field :password_confirmation, 
                required: true, 
                autocomplete: "new-password",
                placeholder: "Confirm your password",
                maxlength: 72,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm #{'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' if @user.errors[:password_confirmation].any?}",
                data: { "password-strength-target": "confirmation", action: "input->password-strength#checkMatch" },
                aria: { describedby: @user.errors[:password_confirmation].any? ? "password-confirmation-error" : nil } %>
          </div>
          <div class="mt-1" data-password-strength-target="match" style="display: none;">
            <p class="text-xs" data-password-strength-target="matchText"></p>
          </div>
          <% if @user.errors[:password_confirmation].any? %>
            <p class="mt-2 text-sm text-red-600" id="password-confirmation-error">
              <%= @user.errors[:password_confirmation].first %>
            </p>
          <% end %>
        </div>

        <!-- Terms and Privacy -->
        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :terms_accepted, 
                required: true,
                class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded #{'border-red-300 text-red-900 focus:ring-red-500' if @user.errors[:terms_accepted].any?}",
                aria: { describedby: @user.errors[:terms_accepted].any? ? "terms-error" : nil } %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :terms_accepted, class: "font-medium text-gray-700" do %>
              I agree to the 
              <a href="#" class="text-indigo-600 hover:text-indigo-500">Terms of Service</a>
              and 
              <a href="#" class="text-indigo-600 hover:text-indigo-500">Privacy Policy</a>
            <% end %>
          </div>
        </div>
        <% if @user.errors[:terms_accepted].any? %>
          <p class="text-sm text-red-600" id="terms-error">
            <%= @user.errors[:terms_accepted].first %>
          </p>
        <% end %>

        <!-- Submit Button -->
        <div>
          <%= form.submit "Create account", 
              class: "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",
              data: { "password-strength-target": "submit", disable_with: "Creating account..." } %>
        </div>
      <% end %>
    </div>
  </div>
</div>
