<% content_for :title, "Sign Up - DataFlow" %>

<div class="min-h-screen bg-white flex">
  <!-- Left Column - Information -->
  <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-gradient-to-br from-indigo-900 via-indigo-800 to-purple-800">
    <div class="mx-auto w-full max-w-sm lg:w-96">
      <!-- Logo -->
      <div class="flex items-center mb-8">
        <div class="h-10 w-10 bg-white rounded-lg flex items-center justify-center">
          <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <span class="ml-3 text-2xl font-bold text-white">DataFlow</span>
      </div>

      <!-- Hero Content -->
      <div class="text-white">
        <h1 class="text-4xl font-extrabold leading-tight">
          Transform Your Data Into
          <span class="text-indigo-300">Actionable Insights</span>
        </h1>
        <p class="mt-6 text-lg text-indigo-100 leading-relaxed">
          Join thousands of professionals who use DataFlow to automatically analyze their data and generate beautiful presentations with AI-powered insights.
        </p>
      </div>

      <!-- Features -->
      <div class="mt-10 space-y-6">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-white">AI-Powered Analysis</h3>
            <p class="mt-1 text-indigo-200">Upload Excel, CSV, or JSON files and get instant intelligent insights</p>
          </div>
        </div>

        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-white">Auto-Generated Presentations</h3>
            <p class="mt-1 text-indigo-200">Beautiful PDF presentations created automatically from your data</p>
          </div>
        </div>

        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-500">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-white">Enterprise Security</h3>
            <p class="mt-1 text-indigo-200">Your data is encrypted and secure with enterprise-grade protection</p>
          </div>
        </div>
      </div>

      <!-- Testimonial -->
      <div class="mt-10 p-6 bg-white bg-opacity-10 rounded-lg backdrop-blur-sm">
        <blockquote class="text-indigo-100 italic">
          "DataFlow saved me hours of manual analysis. The AI insights are incredibly accurate and the presentations look professional."
        </blockquote>
        <div class="mt-3 flex items-center">
          <div class="h-8 w-8 bg-indigo-300 rounded-full flex items-center justify-center">
            <span class="text-sm font-medium text-indigo-800">SJ</span>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-white">Sarah Johnson</p>
            <p class="text-xs text-indigo-200">Data Analyst, TechCorp</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Column - Sign Up Form -->
  <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
    <div class="mx-auto w-full max-w-sm lg:w-96">
      <div>
        <h2 class="text-3xl font-extrabold text-gray-900">Create your account</h2>
        <p class="mt-2 text-sm text-gray-600">
          Already have an account?
          <%= link_to "Sign in here", new_session_path,
              class: "font-medium text-indigo-600 hover:text-indigo-500" %>
        </p>
      </div>

      <div class="mt-8">
        <%= form_with model: @user, local: true,
            class: "space-y-6",
            data: { controller: "password-strength" } do |form| %>

          <!-- Display validation errors -->
          <% if @user.errors.any? %>
            <div class="rounded-md bg-red-50 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Please fix the following:</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc pl-5 space-y-1">
                      <% @user.errors.full_messages.each do |message| %>
                        <li><%= message %></li>
                      <% end %>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Email Address -->
          <div>
            <%= form.label :email_address, "Email address", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.email_field :email_address,
                  required: true,
                  autofocus: true,
                  autocomplete: "email",
                  placeholder: "<EMAIL>",
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
            </div>
          </div>

          <!-- Password -->
          <div>
            <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.password_field :password,
                  required: true,
                  autocomplete: "new-password",
                  placeholder: "Create a strong password",
                  maxlength: 72,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                  data: { "password-strength-target": "password", action: "input->password-strength#checkStrength" } %>
            </div>

            <!-- Password strength indicator -->
            <div class="mt-2" data-password-strength-target="indicator" style="display: none;">
              <div class="flex items-center space-x-2">
                <div class="flex-1 bg-gray-200 rounded-full h-1.5">
                  <div class="h-1.5 rounded-full transition-all duration-300" data-password-strength-target="bar"></div>
                </div>
                <span class="text-xs font-medium" data-password-strength-target="text"></span>
              </div>
            </div>

            <!-- Password requirements -->
            <div class="mt-2 text-xs text-gray-500">
              Must be 8+ characters with uppercase, lowercase, number, and special character
            </div>
          </div>

          <!-- Password Confirmation -->
          <div>
            <%= form.label :password_confirmation, "Confirm password", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.password_field :password_confirmation,
                  required: true,
                  autocomplete: "new-password",
                  placeholder: "Confirm your password",
                  maxlength: 72,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
            </div>
          </div>

          <!-- Terms and Privacy -->
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <%= form.check_box :terms_accepted,
                  required: true,
                  class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
            </div>
            <div class="ml-3 text-sm">
              <%= form.label :terms_accepted, class: "text-gray-700" do %>
                I agree to the
                <a href="#" class="text-indigo-600 hover:text-indigo-500 underline">Terms of Service</a>
                and
                <a href="#" class="text-indigo-600 hover:text-indigo-500 underline">Privacy Policy</a>
              <% end %>
            </div>
          </div>

          <!-- Submit Button -->
          <div>
            <%= form.submit "Create account",
                class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out",
                data: { disable_with: "Creating account..." } %>
          </div>

          <!-- Additional Info -->
          <div class="text-center">
            <p class="text-xs text-gray-500">
              By creating an account, you'll be able to upload data files and generate AI-powered insights instantly.
            </p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
