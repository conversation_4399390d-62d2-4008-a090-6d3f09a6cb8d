<div class="bg-white">
  <!-- Header -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="text-center">
      <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
        Simple, transparent pricing
      </h2>
      <p class="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
        Choose the perfect plan for your data automation needs. Upgrade or downgrade at any time.
      </p>
    </div>

    <!-- Pricing Cards -->
    <div class="mt-16 grid grid-cols-1 gap-8 lg:grid-cols-3">
      <% @plans.each_with_index do |plan, index| %>
        <div class="relative <%= 'border-2 border-indigo-600' if index == 1 %> bg-white border border-gray-200 rounded-xl shadow-sm divide-y divide-gray-200">
          
          <% if index == 1 %>
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-indigo-600 text-white px-4 py-1 text-sm font-medium rounded-full">
                Most Popular
              </span>
            </div>
          <% end %>

          <div class="p-8">
            <!-- Plan Name -->
            <h3 class="text-2xl font-semibold text-gray-900"><%= plan.name %></h3>
            
            <!-- Price -->
            <div class="mt-4 flex items-baseline">
              <% if plan.free_plan? %>
                <span class="text-5xl font-bold tracking-tight text-gray-900">Free</span>
              <% else %>
                <span class="text-5xl font-bold tracking-tight text-gray-900">$<%= plan.price.to_i %></span>
                <span class="ml-1 text-xl font-semibold text-gray-500">/month</span>
              <% end %>
            </div>
            
            <!-- Upload Limit -->
            <p class="mt-6 text-gray-500"><%= plan.upload_limit_text %></p>

            <!-- CTA Button -->
            <div class="mt-8">
              <% if @current_plan&.id == plan.id %>
                <button disabled class="w-full bg-gray-100 text-gray-500 py-3 px-4 rounded-lg font-medium cursor-not-allowed">
                  Current Plan
                </button>
              <% elsif Current.user %>
                <%= link_to new_subscription_path(plan_id: plan.id), 
                    class: "w-full block text-center #{index == 1 ? 'bg-indigo-600 hover:bg-indigo-700 text-white' : 'bg-indigo-50 hover:bg-indigo-100 text-indigo-600'} py-3 px-4 rounded-lg font-medium transition-colors" do %>
                  <%= @current_subscription ? 'Switch to this plan' : 'Get started' %>
                <% end %>
              <% else %>
                <%= link_to signup_path, 
                    class: "w-full block text-center #{index == 1 ? 'bg-indigo-600 hover:bg-indigo-700 text-white' : 'bg-indigo-50 hover:bg-indigo-100 text-indigo-600'} py-3 px-4 rounded-lg font-medium transition-colors" do %>
                  Sign up
                <% end %>
              <% end %>
            </div>
          </div>

          <!-- Features List -->
          <div class="px-8 pt-6 pb-8">
            <h4 class="text-sm font-medium text-gray-900 tracking-wide uppercase">
              What's included
            </h4>
            <ul class="mt-6 space-y-4">
              <% plan.features_list.each do |feature| %>
                <li class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                    </svg>
                  </div>
                  <p class="ml-3 text-sm text-gray-700"><%= feature %></p>
                </li>
              <% end %>
            </ul>
          </div>
        </div>
      <% end %>
    </div>

    <!-- FAQ Section -->
    <div class="mt-16">
      <h3 class="text-2xl font-bold text-gray-900 text-center mb-8">
        Frequently asked questions
      </h3>
      <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
        <div>
          <h4 class="text-lg font-medium text-gray-900">Can I change plans anytime?</h4>
          <p class="mt-2 text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes are prorated automatically.</p>
        </div>
        <div>
          <h4 class="text-lg font-medium text-gray-900">What file types are supported?</h4>
          <p class="mt-2 text-gray-600">We support Excel (.xlsx, .xls), CSV, JSON, and text files for comprehensive data analysis.</p>
        </div>
        <div>
          <h4 class="text-lg font-medium text-gray-900">Is my data secure?</h4>
          <p class="mt-2 text-gray-600">Absolutely. We use enterprise-grade encryption and never store your sensitive data permanently.</p>
        </div>
        <div>
          <h4 class="text-lg font-medium text-gray-900">Do you offer refunds?</h4>
          <p class="mt-2 text-gray-600">Yes, we offer a 30-day money-back guarantee on all paid plans.</p>
        </div>
      </div>
    </div>

    <!-- Usage Stats for Logged-in Users -->
    <% if Current.user&.current_subscription %>
      <div class="mt-16 bg-gray-50 rounded-xl p-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6">Your Current Usage</h3>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
          <div class="text-center">
            <div class="text-3xl font-bold text-indigo-600"><%= Current.user.monthly_uploads_count %></div>
            <div class="text-sm text-gray-500">Uploads this month</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600"><%= Current.user.uploads_remaining %></div>
            <div class="text-sm text-gray-500">Uploads remaining</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600"><%= @current_plan.name %></div>
            <div class="text-sm text-gray-500">Current plan</div>
          </div>
        </div>
        
        <div class="mt-6">
          <%= link_to "Manage Subscription", subscriptions_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700" %>
        </div>
      </div>
    <% end %>
  </div>
</div>
