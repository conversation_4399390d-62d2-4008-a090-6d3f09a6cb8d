<!DOCTYPE html>
<html class="h-full bg-gray-50">
  <head>
    <title><%= content_for(:title) || "DataFlow - Intelligent Data Automation" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps %>
    <%= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="h-full">
    <div class="min-h-full">
      <!-- Navigation -->
      <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex">
              <!-- Logo -->
              <div class="flex-shrink-0 flex items-center">
                <%= link_to root_path, class: "flex items-center" do %>
                  <div class="h-8 w-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                    <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <span class="ml-2 text-xl font-semibold text-gray-900">DataFlow</span>
                <% end %>
              </div>

              <!-- Navigation Links (if authenticated) -->
              <% if authenticated? %>
                <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                  <%= link_to "Dashboard", root_path, 
                      class: "border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium #{'border-indigo-500' if current_page?(root_path)}" %>
                  <%= link_to "Data Uploads", data_uploads_path, 
                      class: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium #{'border-indigo-500 text-gray-900' if current_page?(data_uploads_path)}" %>
                  <%= link_to "Upload New", new_data_upload_path, 
                      class: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium" %>
                </div>
              <% end %>
            </div>

            <!-- Right side menu -->
            <div class="hidden sm:ml-6 sm:flex sm:items-center">
              <% if authenticated? %>
                <!-- User menu -->
                <div class="ml-3 relative">
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-700">
                      <%= Current.user.email_address %>
                    </span>
                    <%= button_to "Sign out", session_path, 
                        method: :delete, 
                        class: "bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
                  </div>
                </div>
              <% else %>
                <!-- Auth links -->
                <div class="flex items-center space-x-4">
                  <%= link_to "Sign in", new_session_path,
                      class: "text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium" %>
                  <%= link_to "Get Started", new_user_path,
                      class: "bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium" %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </nav>

      <!-- Flash messages -->
      <% if notice %>
        <div class="bg-green-50 border border-green-200 rounded-md p-4 mx-4 mt-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-green-800"><%= notice %></p>
            </div>
          </div>
        </div>
      <% end %>

      <% if alert %>
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mx-4 mt-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800"><%= alert %></p>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Main content -->
      <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <%= yield %>
      </main>
    </div>
  </body>
</html>
