<% content_for :title, "Sign In - DataFlow" %>

<div class="min-h-screen bg-white flex">
  <!-- Left Column - Information -->
  <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-gradient-to-br from-indigo-900 via-indigo-800 to-purple-800">
    <div class="mx-auto w-full max-w-sm lg:w-96">
      <!-- Logo -->
      <div class="flex items-center mb-8">
        <div class="h-10 w-10 bg-white rounded-lg flex items-center justify-center">
          <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <span class="ml-3 text-2xl font-bold text-white">DataFlow</span>
      </div>

      <!-- Hero Content -->
      <div class="text-white">
        <h1 class="text-4xl font-extrabold leading-tight">
          Welcome Back to
          <span class="text-indigo-300">DataFlow</span>
        </h1>
        <p class="mt-6 text-lg text-indigo-100 leading-relaxed">
          Continue transforming your data into actionable insights with our AI-powered platform.
        </p>
      </div>

      <!-- Quick Stats -->
      <div class="mt-10 grid grid-cols-2 gap-4">
        <div class="bg-white bg-opacity-10 rounded-lg p-4 backdrop-blur-sm">
          <div class="text-2xl font-bold text-white">10K+</div>
          <div class="text-sm text-indigo-200">Files Processed</div>
        </div>
        <div class="bg-white bg-opacity-10 rounded-lg p-4 backdrop-blur-sm">
          <div class="text-2xl font-bold text-white">5K+</div>
          <div class="text-sm text-indigo-200">Happy Users</div>
        </div>
      </div>

      <!-- Recent Update -->
      <div class="mt-8 p-4 bg-indigo-600 bg-opacity-50 rounded-lg backdrop-blur-sm border border-indigo-400">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-indigo-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-white">New Feature</h3>
            <p class="mt-1 text-sm text-indigo-200">Enhanced AI analysis now supports real-time data visualization suggestions.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Column - Sign In Form -->
  <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
    <div class="mx-auto w-full max-w-sm lg:w-96">
      <div>
        <h2 class="text-3xl font-extrabold text-gray-900">Sign in to your account</h2>
        <p class="mt-2 text-sm text-gray-600">
          Don't have an account?
          <%= link_to "Create one here", new_user_path,
              class: "font-medium text-indigo-600 hover:text-indigo-500" %>
        </p>
      </div>

      <div class="mt-8">
        <!-- Display flash messages -->
        <% if alert %>
          <div class="rounded-md bg-red-50 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-red-800"><%= alert %></p>
              </div>
            </div>
          </div>
        <% end %>

        <% if notice %>
          <div class="rounded-md bg-green-50 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-green-800"><%= notice %></p>
              </div>
            </div>
          </div>
        <% end %>

        <%= form_with url: session_url, local: true, class: "space-y-6" do |form| %>
          <!-- Email Address -->
          <div>
            <%= form.label :email_address, "Email address", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.email_field :email_address,
                  required: true,
                  autofocus: true,
                  autocomplete: "email",
                  placeholder: "<EMAIL>",
                  value: params[:email_address],
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
            </div>
          </div>

          <!-- Password -->
          <div>
            <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= form.password_field :password,
                  required: true,
                  autocomplete: "current-password",
                  placeholder: "Enter your password",
                  maxlength: 72,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
            </div>
          </div>

          <!-- Remember me and Forgot password -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <%= form.check_box :remember_me, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
              <%= form.label :remember_me, "Remember me", class: "ml-2 block text-sm text-gray-900" %>
            </div>

            <div class="text-sm">
              <%= link_to "Forgot password?", new_password_path,
                  class: "font-medium text-indigo-600 hover:text-indigo-500" %>
            </div>
          </div>

          <!-- Submit Button -->
          <div>
            <%= form.submit "Sign in",
                class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out",
                data: { disable_with: "Signing in..." } %>
          </div>

          <!-- Additional Links -->
          <div class="text-center">
            <p class="text-sm text-gray-600">
              New to DataFlow?
              <%= link_to "Start your free trial", new_user_path,
                  class: "font-medium text-indigo-600 hover:text-indigo-500" %>
            </p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
