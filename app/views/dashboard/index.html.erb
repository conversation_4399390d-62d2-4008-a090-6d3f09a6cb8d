<% content_for :title, "Dashboard - DataFlow" %>

<%= ui_container do %>
  <!-- Welcome Header -->
  <div class="mb-8">
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">
            Welcome back, <%= Current.user.email_address.split('@').first.humanize %>! 👋
          </h1>
          <p class="text-indigo-100 text-lg">
            Ready to transform your data into actionable insights?
          </p>
        </div>
        <div class="hidden lg:block">
          <div class="bg-white bg-opacity-20 rounded-xl p-4 backdrop-blur-sm">
            <svg class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="mt-6 flex flex-wrap gap-3">
        <%= ui_button "Upload New File", variant: :secondary, class: "bg-white bg-opacity-20 border-white border-opacity-30 text-white hover:bg-opacity-30" do %>
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          Upload New File
        <% end %>

        <%= link_to data_uploads_path, class: "btn btn-ghost text-white hover:bg-white hover:bg-opacity-20 border border-white border-opacity-30" do %>
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          View All Files
        <% end %>
      </div>
    </div>
  </div>

  <!-- Stats Overview -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Uploads -->
    <div class="card">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
              <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-600">Total Files</p>
            <p class="text-2xl font-bold text-gray-900"><%= @upload_stats[:total] %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Processing -->
    <div class="card">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <svg class="h-6 w-6 text-yellow-600 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-600">Processing</p>
            <p class="text-2xl font-bold text-gray-900"><%= @upload_stats[:processing] %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Completed -->
    <div class="card">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-600">Completed</p>
            <p class="text-2xl font-bold text-gray-900"><%= @upload_stats[:completed] %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Rate -->
    <div class="card">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-600">Success Rate</p>
            <p class="text-2xl font-bold text-gray-900">
              <%= @upload_stats[:total] > 0 ? "#{((@upload_stats[:completed].to_f / @upload_stats[:total]) * 100).round}%" : "0%" %>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <%= ui_card title: "Recent Activity", subtitle: "Your latest data processing activities" do %>
    <% if @recent_uploads.any? %>
      <div class="space-y-4">
        <% @recent_uploads.each do |upload| %>
          <%= link_to data_upload_path(upload), class: "block" do %>
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <%= file_type_icon(upload.file_type, class: "h-10 w-10") %>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    <%= upload.name %>
                  </p>
                  <p class="text-sm text-gray-500">
                    <%= upload.file_type %> • <%= number_to_human_size(upload.file_size) %> •
                    <%= time_ago_in_words(upload.created_at) %> ago
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <%= ui_badge upload.status.humanize, variant: case upload.status
                    when 'completed' then :success
                    when 'processing' then :warning
                    when 'failed' then :error
                    else :gray
                    end %>
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>

      <div class="mt-6 text-center">
        <%= link_to data_uploads_path, class: "text-indigo-600 hover:text-indigo-900 font-medium text-sm" do %>
          View all uploads →
        <% end %>
      </div>
    <% else %>
      <%= ui_empty_state(
        title: "No uploads yet",
        description: "Get started by uploading your first data file to see AI-powered insights.",
        icon: content_tag(:svg, class: "mx-auto h-12 w-12 text-gray-400", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor") do
          tag(:path, "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2", d: "M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12")
        end,
        action: link_to(new_data_upload_path, class: "btn btn-primary") do
          content_tag(:svg, class: "-ml-1 mr-2 h-5 w-5", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor") do
            tag(:path, "stroke-linecap": "round", "stroke-linejoin": "round", "stroke-width": "2", d: "M12 6v6m0 0v6m0-6h6m-6 0H6")
          end + "Upload File"
        end
      ) %>
    <% end %>
  <% end %>

  <!-- Recent Presentations -->
  <% if @recent_presentations.any? %>
    <%= ui_card title: "Recent Presentations", subtitle: "Your latest generated presentations", class: "mt-8" do %>
      <div class="space-y-4">
        <% @recent_presentations.each do |presentation| %>
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <svg class="h-6 w-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z"/>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                  <%= presentation.title %>
                </p>
                <p class="text-sm text-gray-500">
                  <%= pluralize(presentation.slide_count, 'slide') %> • Generated <%= time_ago_in_words(presentation.generated_at) %> ago
                </p>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <% if presentation.pdf_attached? %>
                <%= ui_button "Download", variant: :secondary, size: :sm,
                    onclick: "window.location.href='#{download_presentation_data_upload_path(presentation.data_upload)}'" do %>
                  <svg class="-ml-1 mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download
                <% end %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>
  <% end %>
<% end %>
