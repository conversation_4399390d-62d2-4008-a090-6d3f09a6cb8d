<% content_for :title, "Dashboard - DataFlow" %>

<!-- Page header -->
<div class="px-4 py-6 sm:px-0">
  <div class="border-4 border-dashed border-gray-200 rounded-lg h-96">
    <!-- Welcome Section -->
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
      <h1 class="mt-4 text-3xl font-bold text-gray-900">Welcome to DataFlow</h1>
      <p class="mt-2 text-lg text-gray-600">Transform your data into actionable insights with AI-powered automation</p>
      
      <%= link_to new_data_upload_path, 
          class: "mt-6 inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        <svg class="-ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        Upload Your First File
      <% end %>
    </div>
  </div>
</div>

<!-- Stats Section -->
<div class="mt-8">
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-3">
    <!-- Total Uploads -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Uploads</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @upload_stats[:total] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Processing -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Processing</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @upload_stats[:processing] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Completed -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @upload_stats[:completed] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activity -->
<div class="mt-8">
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Uploads</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Your latest data processing activities</p>
    </div>
    
    <% if @recent_uploads.any? %>
      <ul role="list" class="divide-y divide-gray-200">
        <% @recent_uploads.each do |upload| %>
          <li>
            <%= link_to data_upload_path(upload), class: "block hover:bg-gray-50" do %>
              <div class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <!-- File type icon -->
                      <% if upload.excel_file? %>
                        <svg class="h-8 w-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 18h12V6h-4V2H4v16zm6-10h2v2h-2V8zm0 4h2v2h-2v-2z"/>
                        </svg>
                      <% elsif upload.csv_file? %>
                        <svg class="h-8 w-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2z"/>
                        </svg>
                      <% else %>
                        <svg class="h-8 w-8 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      <% end %>
                    </div>
                    <div class="ml-4">
                      <p class="text-sm font-medium text-gray-900">
                        <%= upload.name %>
                      </p>
                      <p class="text-sm text-gray-500">
                        <%= upload.file_type %> • <%= number_to_human_size(upload.file_size) %>
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <!-- Status badge -->
                    <% case upload.status %>
                    <% when 'completed' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Completed
                      </span>
                    <% when 'processing' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Processing
                      </span>
                    <% when 'failed' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Failed
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Uploaded
                      </span>
                    <% end %>
                    
                    <div class="ml-4 text-sm text-gray-500">
                      <%= time_ago_in_words(upload.created_at) %> ago
                    </div>
                    
                    <svg class="ml-2 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            <% end %>
          </li>
        <% end %>
      </ul>
      
      <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
        <%= link_to "View all uploads", data_uploads_path, 
            class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No uploads yet</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by uploading your first data file.</p>
        <div class="mt-6">
          <%= link_to new_data_upload_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Upload File
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<!-- Recent Presentations -->
<% if @recent_presentations.any? %>
  <div class="mt-8">
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Presentations</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Your latest generated presentations</p>
      </div>
      
      <ul role="list" class="divide-y divide-gray-200">
        <% @recent_presentations.each do |presentation| %>
          <li class="px-4 py-4 sm:px-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-8 w-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z"/>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-900">
                    <%= presentation.title %>
                  </p>
                  <p class="text-sm text-gray-500">
                    <%= pluralize(presentation.slide_count, 'slide') %> • Generated <%= time_ago_in_words(presentation.generated_at) %> ago
                  </p>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <% if presentation.pdf_attached? %>
                  <%= link_to download_presentation_data_upload_path(presentation.data_upload), 
                      class: "inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
                    <svg class="-ml-1 mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download
                  <% end %>
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    </div>
  </div>
<% end %>
