<% content_for :title, "#{@data_upload.name} - DataFlow" %>

<div class="max-w-7xl mx-auto">
  <!-- Breadcrumb -->
  <nav class="flex mb-8" aria-label="Breadcrumb">
    <ol role="list" class="flex items-center space-x-4">
      <li>
        <div>
          <%= link_to root_path, class: "text-gray-400 hover:text-gray-500" do %>
            <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
            </svg>
            <span class="sr-only">Home</span>
          <% end %>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <%= link_to "Data Uploads", data_uploads_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <span class="ml-4 text-sm font-medium text-gray-500 truncate"><%= @data_upload.name %></span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Header -->
  <div class="lg:flex lg:items-center lg:justify-between mb-8">
    <div class="flex-1 min-w-0">
      <div class="flex items-center">
        <!-- File type icon -->
        <div class="flex-shrink-0">
          <% if @data_upload.excel_file? %>
            <svg class="h-12 w-12 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 18h12V6h-4V2H4v16zm6-10h2v2h-2V8zm0 4h2v2h-2v-2z"/>
            </svg>
          <% elsif @data_upload.csv_file? %>
            <svg class="h-12 w-12 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2z"/>
            </svg>
          <% elsif @data_upload.json_file? %>
            <svg class="h-12 w-12 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          <% else %>
            <svg class="h-12 w-12 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          <% end %>
        </div>
        
        <div class="ml-4">
          <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            <%= @data_upload.name %>
          </h2>
          <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
            <div class="mt-2 flex items-center text-sm text-gray-500">
              <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <%= @data_upload.file_type %>
            </div>
            <div class="mt-2 flex items-center text-sm text-gray-500">
              <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Uploaded <%= time_ago_in_words(@data_upload.created_at) %> ago
            </div>
            <div class="mt-2 flex items-center text-sm text-gray-500">
              <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
              </svg>
              <%= number_to_human_size(@data_upload.file_size) %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-5 flex lg:mt-0 lg:ml-4">
      <!-- Status badge -->
      <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium
        <% case @data_upload.status %>
        <% when 'completed' %>bg-green-100 text-green-800<% end %>
        <% when 'processing' %>bg-yellow-100 text-yellow-800<% end %>
        <% when 'failed' %>bg-red-100 text-red-800<% end %>
        <% when 'uploaded' %>bg-gray-100 text-gray-800<% end %>">
        <% case @data_upload.status %>
        <% when 'completed' %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          Completed
        <% when 'processing' %>
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
          Processing...
        <% when 'failed' %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          Failed
        <% else %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd" />
          </svg>
          Uploaded
        <% end %>
      </span>
    </div>
  </div>

  <!-- Processing Status -->
  <% if @data_upload.status == 'processing' %>
    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-8" 
         data-processing-status-target="statusContainer">
      <div class="flex">
        <div class="flex-shrink-0">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-yellow-600"></div>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">Processing in progress</h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p data-processing-status-target="progressMessage">We're analyzing your data and generating insights. This usually takes a few minutes.</p>
          </div>
          <div class="mt-4">
            <div class="flex items-center justify-between text-sm text-yellow-700 mb-2">
              <span>Progress</span>
              <span data-processing-status-target="progressPercent">0%</span>
            </div>
            <div class="w-full bg-yellow-200 rounded-full h-2">
              <div class="bg-yellow-600 h-2 rounded-full transition-all duration-300 ease-in-out" 
                   data-processing-status-target="progressBar" 
                   style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Error State -->
  <% if @data_upload.status == 'failed' %>
    <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-8">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Processing failed</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>We encountered an error while processing your file. Please try uploading again or contact support if the problem persists.</p>
          </div>
          <div class="mt-4">
            <%= link_to new_data_upload_path, 
                class: "bg-red-100 text-red-800 hover:bg-red-200 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" do %>
              Try Again
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Main Content -->
  <% if @data_upload.status == 'completed' && @data_upload.data_result %>
    <div class="space-y-8">
      <!-- Data Overview -->
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Data Overview</h3>
          
          <% if @data_upload.data_result.parsed_data.present? %>
            <% data_summary = @data_upload.data_result.parsed_data['summary'] %>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2 lg:grid-cols-4">
              <% case @data_upload.data_result.parsed_data['type'] %>
              <% when 'excel' %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Sheets</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= data_summary['sheet_count'] %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Total Rows</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= data_summary['total_rows'] %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Max Columns</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= data_summary['total_columns'] %></dd>
                </div>
              <% when 'csv' %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Rows</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= data_summary['row_count'] %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Columns</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= data_summary['column_count'] %></dd>
                </div>
              <% when 'text' %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Lines</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= data_summary['line_count'] %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Words</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= data_summary['word_count'] %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Characters</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= data_summary['character_count'] %></dd>
                </div>
              <% when 'json' %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Structure</dt>
                  <dd class="mt-1 text-sm text-gray-900">JSON Object</dd>
                </div>
              <% end %>
            </dl>
          <% end %>
        </div>
      </div>

      <!-- AI Insights -->
      <% if @data_upload.data_result.insights.present? %>
        <% insights = @data_upload.data_result.parsed_insights %>
        <div class="bg-white shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">AI-Generated Insights</h3>
            
            <% if insights['summary'] %>
              <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Summary</h4>
                <p class="text-sm text-gray-700"><%= insights['summary'] %></p>
              </div>
            <% end %>
            
            <% if insights['key_insights']&.any? %>
              <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Key Insights</h4>
                <ul class="space-y-2">
                  <% insights['key_insights'].each do |insight| %>
                    <li class="flex items-start">
                      <svg class="flex-shrink-0 h-5 w-5 text-green-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span class="text-sm text-gray-700"><%= insight %></span>
                    </li>
                  <% end %>
                </ul>
              </div>
            <% end %>
            
            <% if insights['recommendations']&.any? %>
              <div>
                <h4 class="text-sm font-medium text-gray-900 mb-2">Recommendations</h4>
                <ul class="space-y-2">
                  <% insights['recommendations'].each do |recommendation| %>
                    <li class="flex items-start">
                      <svg class="flex-shrink-0 h-5 w-5 text-blue-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                      </svg>
                      <span class="text-sm text-gray-700"><%= recommendation %></span>
                    </li>
                  <% end %>
                </ul>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>

      <!-- Visualizations -->
      <% if @data_upload.data_result.visualization_data.present? %>
        <% visualizations = @data_upload.data_result.parsed_visualization_data %>
        <% if visualizations['suggestions']&.any? %>
          <div class="bg-white shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recommended Visualizations</h3>
              
              <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <% visualizations['suggestions'].each do |viz| %>
                  <div class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                    <div class="flex items-center">
                      <div class="flex-shrink-0">
                        <% case viz['chart_type'] %>
                        <% when 'bar' %>
                          <svg class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                        <% when 'line' %>
                          <svg class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4" />
                          </svg>
                        <% when 'table' %>
                          <svg class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        <% else %>
                          <svg class="h-8 w-8 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                          </svg>
                        <% end %>
                      </div>
                      <div class="ml-4">
                        <h4 class="text-sm font-medium text-gray-900"><%= viz['title'] %></h4>
                        <p class="text-sm text-gray-500"><%= viz['chart_type'].humanize %> Chart</p>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>

      <!-- Generated Presentations -->
      <% if @data_upload.presentations.any? %>
        <div class="bg-white shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Generated Presentations</h3>
            
            <div class="space-y-4">
              <% @data_upload.presentations.each do |presentation| %>
                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z"/>
                        </svg>
                      </div>
                      <div class="ml-4">
                        <h4 class="text-sm font-medium text-gray-900"><%= presentation.title %></h4>
                        <p class="text-sm text-gray-500">
                          <%= pluralize(presentation.slide_count, 'slide') %> • 
                          Generated <%= time_ago_in_words(presentation.generated_at) %> ago
                        </p>
                      </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                      <% if presentation.pdf_attached? %>
                        <%= link_to download_presentation_data_upload_path(@data_upload),
                            class: "inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
                          <svg class="-ml-1 mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          Download PDF
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    
  <% elsif @data_upload.status == 'uploaded' %>
    <!-- Uploaded but not processed yet -->
    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">Ready for processing</h3>
          <div class="mt-2 text-sm text-blue-700">
            <p>Your file has been uploaded successfully and is ready for processing. Processing will begin automatically.</p>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Action Buttons -->
  <div class="mt-8 flex justify-between">
    <%= link_to data_uploads_path, 
        class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
      ← Back to Uploads
    <% end %>
    
    <div class="space-x-3">
      <% if @data_upload.status == 'failed' %>
        <%= link_to new_data_upload_path, 
            class: "bg-indigo-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          Upload Again
        <% end %>
      <% end %>
    </div>
  </div>
</div>

<!-- WebSocket controller for real-time updates -->
<% if @data_upload.status == 'processing' %>
  <div data-controller="processing-status" 
       data-processing-status-upload-id-value="<%= @data_upload.id %>"
       data-processing-status-current-status-value="<%= @data_upload.status %>">
  </div>
<% end %>
