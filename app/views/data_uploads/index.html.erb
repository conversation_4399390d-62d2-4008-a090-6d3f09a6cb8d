<% content_for :title, "Data Uploads - DataFlow" %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Data Uploads
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Manage and track all your uploaded data files
      </p>
    </div>
    <div class="mt-4 flex md:mt-0 md:ml-4">
      <%= link_to new_data_upload_path,
          class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Upload New File
      <% end %>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white shadow rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
      <%= form_tag data_uploads_path, method: :get, local: true, class: "space-y-4 sm:space-y-0 sm:flex sm:items-center sm:space-x-4" do %>
        <div class="flex-1">
          <%= text_field_tag :search, params[:search], 
              placeholder: "Search uploads...", 
              class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
        </div>
        
        <div>
          <%= select_tag :status, 
              options_for_select([
                ['All Status', ''],
                ['Uploaded', 'uploaded'],
                ['Processing', 'processing'],
                ['Completed', 'completed'],
                ['Failed', 'failed']
              ], params[:status]),
              class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
        </div>
        
        <div>
          <%= select_tag :file_type,
              options_for_select([
                ['All Types', ''],
                ['Excel Files', 'excel'],
                ['CSV Files', 'csv'],
                ['Text Files', 'text'],
                ['JSON Files', 'json']
              ], params[:file_type]),
              class: "shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" %>
        </div>
        
        <div class="flex space-x-2">
          <%= submit_tag "Filter", 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          <%= link_to "Clear", data_uploads_path,
              class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Upload List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @data_uploads.any? %>
      <ul role="list" class="divide-y divide-gray-200">
        <% @data_uploads.each do |upload| %>
          <li>
            <%= link_to data_upload_path(upload), class: "block hover:bg-gray-50 transition-colors duration-150" do %>
              <div class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <!-- File type icon -->
                      <% if upload.excel_file? %>
                        <svg class="h-10 w-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 18h12V6h-4V2H4v16zm6-10h2v2h-2V8zm0 4h2v2h-2v-2z"/>
                        </svg>
                      <% elsif upload.csv_file? %>
                        <svg class="h-10 w-10 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2z"/>
                        </svg>
                      <% elsif upload.json_file? %>
                        <svg class="h-10 w-10 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                        </svg>
                      <% else %>
                        <svg class="h-10 w-10 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      <% end %>
                    </div>
                    <div class="ml-4">
                      <div class="flex items-center">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= upload.name %>
                        </p>
                        <!-- Processing indicator -->
                        <% if upload.status == 'processing' %>
                          <div class="ml-2 flex-shrink-0">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                          </div>
                        <% end %>
                      </div>
                      <div class="mt-1 flex items-center text-sm text-gray-500">
                        <p class="truncate">
                          <%= upload.file_type %> • <%= number_to_human_size(upload.file_size) %>
                        </p>
                        <span class="mx-2">•</span>
                        <p>
                          Uploaded <%= time_ago_in_words(upload.created_at) %> ago
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-4">
                    <!-- Status badge -->
                    <% case upload.status %>
                    <% when 'completed' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Completed
                      </span>
                    <% when 'processing' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Processing
                      </span>
                    <% when 'failed' %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Failed
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Uploaded
                      </span>
                    <% end %>
                    
                    <!-- Presentations count -->
                    <% if upload.presentations.any? %>
                      <div class="flex items-center text-sm text-gray-500">
                        <svg class="mr-1 h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z"/>
                        </svg>
                        <%= pluralize(upload.presentations.count, 'presentation') %>
                      </div>
                    <% end %>
                    
                    <!-- Arrow icon -->
                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                
                <!-- Progress bar for processing files -->
                <% if upload.status == 'processing' %>
                  <div class="mt-3">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="bg-indigo-600 h-2 rounded-full animate-pulse" style="width: 65%"></div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Processing your data...</p>
                  </div>
                <% end %>
                
                <!-- Error message for failed uploads -->
                <% if upload.status == 'failed' %>
                  <div class="mt-3">
                    <p class="text-sm text-red-600">
                      Processing failed. Click to view details and retry.
                    </p>
                  </div>
                <% end %>
              </div>
            <% end %>
          </li>
        <% end %>
      </ul>
      
    <% else %>
      <!-- Empty state -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No uploads found</h3>
        <p class="mt-1 text-sm text-gray-500">
          <% if params[:search].present? || params[:status].present? || params[:file_type].present? %>
            No uploads match your current filters. Try adjusting your search criteria.
          <% else %>
            Get started by uploading your first data file.
          <% end %>
        </p>
        <div class="mt-6">
          <% if params[:search].present? || params[:status].present? || params[:file_type].present? %>
            <%= link_to data_uploads_path,
                class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
              Clear Filters
            <% end %>
          <% else %>
            <%= link_to new_data_upload_path,
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Upload Your First File
            <% end %>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
