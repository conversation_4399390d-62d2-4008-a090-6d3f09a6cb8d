<% content_for :title, "Upload Data - DataFlow" %>

<div class="max-w-3xl mx-auto">
  <!-- Header -->
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="flex-1 min-w-0">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
        Upload Your Data
      </h2>
      <p class="mt-1 text-sm text-gray-500">
        Upload Excel, CSV, Text, or JSON files to generate intelligent insights and presentations
      </p>
    </div>
  </div>

  <!-- Upload Form -->
  <div class="bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with model: @data_upload, local: true, multipart: true, 
          class: "space-y-6", 
          data: { controller: "file-upload" } do |form| %>
        
        <!-- File Upload Area -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Select File
          </label>
          
          <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors"
               data-file-upload-target="dropzone">
            <div class="space-y-1 text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              
              <div class="flex text-sm text-gray-600">
                <label for="data_upload_file" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                  <span>Upload a file</span>
                  <%= form.file_field :file, 
                      id: "data_upload_file",
                      class: "sr-only",
                      accept: ".xlsx,.xls,.csv,.txt,.json",
                      data: { file_upload_target: "input", action: "change->file-upload#fileSelected" } %>
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              
              <p class="text-xs text-gray-500">
                Excel, CSV, TXT, JSON up to 50MB
              </p>
              
              <!-- File preview -->
              <div data-file-upload-target="preview" class="hidden mt-4">
                <div class="flex items-center justify-center space-x-2">
                  <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div>
                    <p class="text-sm font-medium text-gray-900" data-file-upload-target="filename"></p>
                    <p class="text-xs text-gray-500" data-file-upload-target="filesize"></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Supported File Types -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h3 class="text-sm font-medium text-gray-900 mb-3">Supported File Types</h3>
          
          <div class="grid grid-cols-2 gap-4">
            <div class="flex items-center space-x-2">
              <svg class="h-6 w-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 18h12V6h-4V2H4v16zm6-10h2v2h-2V8zm0 4h2v2h-2v-2z"/>
              </svg>
              <div>
                <p class="text-sm font-medium text-gray-900">Excel Files</p>
                <p class="text-xs text-gray-500">.xlsx, .xls</p>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <svg class="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 2h12a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2z"/>
              </svg>
              <div>
                <p class="text-sm font-medium text-gray-900">CSV Files</p>
                <p class="text-xs text-gray-500">.csv</p>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <div>
                <p class="text-sm font-medium text-gray-900">Text Files</p>
                <p class="text-xs text-gray-500">.txt</p>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <svg class="h-6 w-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
              <div>
                <p class="text-sm font-medium text-gray-900">JSON Files</p>
                <p class="text-xs text-gray-500">.json</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Processing Options -->
        <div class="bg-blue-50 rounded-lg p-4">
          <h3 class="text-sm font-medium text-blue-900 mb-2">What happens after upload?</h3>
          <ul class="text-sm text-blue-800 space-y-1">
            <li class="flex items-center">
              <svg class="h-4 w-4 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Automatic data parsing and validation
            </li>
            <li class="flex items-center">
              <svg class="h-4 w-4 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              AI-powered insights and pattern detection
            </li>
            <li class="flex items-center">
              <svg class="h-4 w-4 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Automated presentation generation
            </li>
            <li class="flex items-center">
              <svg class="h-4 w-4 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Visualization recommendations
            </li>
          </ul>
        </div>

        <!-- Error Display -->
        <% if @data_upload.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Please fix the following issues:</h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @data_upload.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-5">
          <%= link_to "Cancel", data_uploads_path, 
              class: "bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
          
          <%= form.submit "Upload and Process", 
              class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",
              data: { file_upload_target: "submit" },
              disabled: true %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Help Section -->
  <div class="mt-8 bg-gray-50 rounded-lg p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Need Help?</h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-2">File Requirements</h4>
        <ul class="text-sm text-gray-600 space-y-1">
          <li>• Maximum file size: 50MB</li>
          <li>• Excel files: First row should contain headers</li>
          <li>• CSV files: UTF-8 encoding preferred</li>
          <li>• JSON files: Valid JSON structure required</li>
        </ul>
      </div>
      
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-2">Processing Time</h4>
        <ul class="text-sm text-gray-600 space-y-1">
          <li>• Small files (&lt;1MB): 1-2 minutes</li>
          <li>• Medium files (1-10MB): 2-5 minutes</li>
          <li>• Large files (10-50MB): 5-15 minutes</li>
          <li>• Real-time progress updates available</li>
        </ul>
      </div>
    </div>
  </div>
</div>
