class SubscriptionsController < ApplicationController
  before_action :set_subscription, only: [:show, :edit, :update, :destroy]

  def index
    @current_subscription = Current.user.current_subscription
    @current_plan = Current.user.current_plan
    @usage_stats = {
      uploads_this_month: Current.user.monthly_uploads_count,
      uploads_remaining: Current.user.uploads_remaining,
      usage_percentage: Current.user.current_plan ? 
        (Current.user.monthly_uploads_count.to_f / Current.user.current_plan.monthly_uploads * 100).round(1) : 0
    }
  end

  def new
    @plans = Plan.active.by_price
    @selected_plan = Plan.find_by(id: params[:plan_id]) || @plans.second
  end

  def create
    @plan = Plan.find(params[:plan_id])
    
    begin
      # Create Stripe customer if needed
      Current.user.create_stripe_customer! unless Current.user.stripe_customer_id
      
      # For demo purposes, create a mock subscription for free plan
      if @plan.free_plan?
        @subscription = Current.user.subscriptions.create!(
          plan: @plan,
          stripe_subscription_id: "demo_sub_#{SecureRandom.hex(8)}",
          status: 'active',
          current_period_start: Time.current,
          current_period_end: 1.month.from_now
        )
        
        redirect_to subscriptions_path, 
                    notice: "Successfully subscribed to #{@plan.name} plan!"
        return
      end
      
      # For paid plans, create Stripe subscription (requires real Stripe keys)
      redirect_to new_subscription_path(plan_id: @plan.id), 
                  alert: 'Stripe integration requires API keys. Contact support for paid plans.'
      
    rescue => e
      redirect_to new_subscription_path(plan_id: @plan.id), 
                  alert: "Subscription failed: #{e.message}"
    end
  end

  def edit
    @plans = Plan.active.by_price
  end

  def update
    @new_plan = Plan.find(params[:plan_id])
    
    # Simple plan update for demo
    @subscription.update!(plan: @new_plan)
    
    redirect_to subscriptions_path, 
                notice: "Successfully updated to #{@new_plan.name} plan!"
  end

  def destroy
    @subscription.update!(status: 'canceled')
    
    redirect_to subscriptions_path, 
                notice: 'Subscription canceled successfully.'
  end

  private

  def set_subscription
    @subscription = Current.user.current_subscription
    redirect_to subscriptions_path, alert: 'No active subscription found.' unless @subscription
  end
end
