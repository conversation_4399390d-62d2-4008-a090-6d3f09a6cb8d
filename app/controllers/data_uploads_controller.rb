class DataUploads<PERSON>ontroller < ApplicationController
  before_action :set_data_upload, only: [:show, :destroy, :process_upload, :download_presentation]

  def index
    @data_uploads = Current.user.data_uploads.recent.includes(:data_result, :presentations)
  end

  def show
    @data_result = @data_upload.data_result
    @presentations = @data_upload.presentations.recent
  end

  def new
    @data_upload = Current.user.data_uploads.build
  end

  def create
    @data_upload = Current.user.data_uploads.build(data_upload_params)
    
    if params[:data_upload][:file].present?
      file = params[:data_upload][:file]
      @data_upload.name = file.original_filename
      @data_upload.file_type = file.content_type
      @data_upload.file_size = file.size
      @data_upload.status = 'uploaded'
      @data_upload.file.attach(file)
    end

    if @data_upload.save
      # Enqueue background processing job
      DataProcessingJob.perform_later(@data_upload.id)
      
      redirect_to @data_upload, notice: 'File uploaded successfully! Processing will begin shortly.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def process_upload
    if @data_upload.uploaded?
      DataProcessingJob.perform_later(@data_upload.id)
      redirect_to @data_upload, notice: 'Processing started!'
    else
      redirect_to @data_upload, alert: 'File cannot be processed in current state.'
    end
  end

  def download_presentation
    if @data_upload.presentations.any?
      presentation = @data_upload.presentations.recent.first
      if presentation.pdf_attached?
        redirect_to rails_blob_path(presentation.pdf_file, disposition: "attachment")
      else
        redirect_to @data_upload, alert: 'Presentation PDF not available.'
      end
    else
      redirect_to @data_upload, alert: 'No presentations available.'
    end
  end

  def destroy
    @data_upload.destroy
    redirect_to data_uploads_path, notice: 'Upload deleted successfully.'
  end

  private

  def set_data_upload
    @data_upload = Current.user.data_uploads.find(params[:id])
  end

  def data_upload_params
    params.require(:data_upload).permit(:name)
  end
end
