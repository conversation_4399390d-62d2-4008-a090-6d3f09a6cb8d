class DashboardController < ApplicationController
  before_action :require_authentication

  def index
    @recent_uploads = Current.user.recent_uploads(5)
    @upload_stats = {
      total: Current.user.uploads_count,
      processing: Current.user.processing_uploads_count,
      completed: Current.user.completed_uploads_count
    }
    @recent_presentations = Current.user.presentations.joins(:data_upload).recent.limit(3)
  end
end
