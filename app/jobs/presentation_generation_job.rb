class PresentationGenerationJob < ApplicationJob
  queue_as :presentation_generation
  
  retry_on StandardError, wait: :exponentially_longer, attempts: 2
  discard_on ActiveRecord::RecordNotFound

  def perform(data_upload_id, template_type = 'business')
    data_upload = DataUpload.find(data_upload_id)
    data_result = data_upload.data_result
    
    return unless data_result&.has_insights?
    
    begin
      # Generate presentation slides
      broadcast_progress_update(data_upload, 97, 'Creating presentation slides...')
      slides_data = generate_slides(data_result, template_type)
      
      # Create presentation record
      presentation = data_upload.presentations.create!(
        title: "Analysis: #{data_upload.name}",
        slides_data: slides_data.to_json,
        generated_at: Time.current
      )
      
      # Generate PDF
      broadcast_progress_update(data_upload, 99, 'Generating PDF presentation...')
      pdf_content = generate_pdf(slides_data)
      
      # Attach PDF to presentation
      presentation.pdf_file.attach(
        io: StringIO.new(pdf_content),
        filename: presentation.filename,
        content_type: 'application/pdf'
      )
      
      # Broadcast final completion
      broadcast_progress_update(data_upload, 100, 'Presentation ready for download!')
      
      Rails.logger.info "Presentation generated successfully for DataUpload #{data_upload_id}"
      
    rescue => e
      Rails.logger.error "Presentation generation failed for DataUpload #{data_upload_id}: #{e.message}"
      # Broadcast failure
      ProcessingStatusChannel.broadcast_to(
        data_upload,
        {
          type: 'processing_failed',
          error: "Presentation generation failed: #{e.message}",
          updated_at: Time.current
        }
      )
      raise e
    end
  end

  private

  def broadcast_progress_update(data_upload, progress, message)
    ProcessingStatusChannel.broadcast_to(
      data_upload,
      {
        type: 'progress_update',
        progress: progress,
        message: message,
        updated_at: Time.current
      }
    )
  end

  def generate_slides(data_result, template_type)
    insights = data_result.parsed_insights
    visualizations = data_result.parsed_visualization_data
    raw_data = data_result.parsed_data
    
    slides = []
    
    # Title slide
    slides << {
      type: 'title',
      title: 'Data Analysis Report',
      subtitle: "Analysis of #{raw_data['type']} data",
      timestamp: Time.current.strftime('%B %d, %Y')
    }
    
    # Executive summary slide
    slides << {
      type: 'summary',
      title: 'Executive Summary',
      content: insights['summary'] || 'Comprehensive analysis of uploaded data',
      key_points: insights['key_insights'] || []
    }
    
    # Data overview slide
    slides << {
      type: 'overview',
      title: 'Data Overview',
      content: build_data_overview_content(raw_data),
      stats: extract_data_stats(raw_data)
    }
    
    # Key insights slide
    if insights['key_insights']&.any?
      slides << {
        type: 'insights',
        title: 'Key Insights',
        insights: insights['key_insights'],
        content: 'Analysis reveals the following important patterns:'
      }
    end
    
    # Visualizations slide
    if visualizations['suggestions']&.any?
      slides << {
        type: 'visualizations',
        title: 'Data Visualizations',
        content: 'Recommended visualizations for better understanding:',
        charts: visualizations['suggestions'].take(4)
      }
    end
    
    # Recommendations slide
    if insights['recommendations']&.any?
      slides << {
        type: 'recommendations',
        title: 'Recommendations',
        content: 'Based on the analysis, we recommend:',
        recommendations: insights['recommendations']
      }
    end
    
    # Next steps slide
    slides << {
      type: 'next_steps',
      title: 'Next Steps',
      content: 'Suggested actions for moving forward:',
      steps: [
        'Review and validate the findings',
        'Implement recommended improvements',
        'Monitor key metrics regularly',
        'Schedule follow-up analysis'
      ]
    }
    
    {
      slides: slides,
      template: template_type,
      generated_at: Time.current
    }
  end

  def generate_pdf(slides_data)
    require 'prawn'
    require 'prawn/table'
    
    pdf = Prawn::Document.new(page_size: 'A4', margin: 40)
    
    slides_data[:slides].each_with_index do |slide, index|
      # Start new page for each slide (except first)
      pdf.start_new_page unless index == 0
      
      case slide[:type]
      when 'title'
        generate_title_slide(pdf, slide)
      when 'summary', 'overview', 'insights', 'recommendations', 'next_steps'
        generate_content_slide(pdf, slide)
      when 'visualizations'
        generate_visualization_slide(pdf, slide)
      end
    end
    
    pdf.render
  end

  def generate_title_slide(pdf, slide)
    pdf.font_size 36
    pdf.text slide[:title], align: :center, style: :bold
    
    pdf.move_down 20
    
    pdf.font_size 18
    pdf.text slide[:subtitle], align: :center
    
    pdf.move_down 40
    
    pdf.font_size 12
    pdf.text slide[:timestamp], align: :center
  end

  def generate_content_slide(pdf, slide)
    # Title
    pdf.font_size 24
    pdf.text slide[:title], style: :bold
    
    pdf.move_down 20
    
    # Content
    pdf.font_size 12
    pdf.text slide[:content] if slide[:content]
    
    pdf.move_down 10
    
    # Bullet points for various content types
    if slide[:key_points]&.any?
      slide[:key_points].each do |point|
        pdf.text "• #{point}", indent_paragraphs: 20
      end
    end
    
    if slide[:insights]&.any?
      slide[:insights].each do |insight|
        pdf.text "• #{insight}", indent_paragraphs: 20
      end
    end
    
    if slide[:recommendations]&.any?
      slide[:recommendations].each do |rec|
        pdf.text "• #{rec}", indent_paragraphs: 20
      end
    end
    
    if slide[:steps]&.any?
      slide[:steps].each_with_index do |step, index|
        pdf.text "#{index + 1}. #{step}", indent_paragraphs: 20
      end
    end
    
    if slide[:stats]&.any?
      pdf.move_down 15
      pdf.text "Key Statistics:", style: :bold
      slide[:stats].each do |stat|
        pdf.text "• #{stat}", indent_paragraphs: 20
      end
    end
  end

  def generate_visualization_slide(pdf, slide)
    pdf.font_size 24
    pdf.text slide[:title], style: :bold
    
    pdf.move_down 20
    
    pdf.font_size 12
    pdf.text slide[:content]
    
    pdf.move_down 15
    
    if slide[:charts]&.any?
      slide[:charts].each do |chart|
        pdf.text "• #{chart[:title]} (#{chart[:chart_type]})", indent_paragraphs: 20
      end
    end
  end

  def build_data_overview_content(raw_data)
    case raw_data['type']
    when 'excel'
      "Excel spreadsheet with #{raw_data['summary']['sheet_count']} sheets containing #{raw_data['summary']['total_rows']} total rows"
    when 'csv'
      "CSV file with #{raw_data['summary']['row_count']} rows and #{raw_data['summary']['column_count']} columns"
    when 'json'
      "JSON file with structured data"
    when 'text'
      "Text file with #{raw_data['summary']['line_count']} lines and #{raw_data['summary']['word_count']} words"
    else
      "Data file successfully processed"
    end
  end

  def extract_data_stats(raw_data)
    stats = []
    
    case raw_data['type']
    when 'excel'
      summary = raw_data['summary']
      stats << "#{summary['sheet_count']} sheets"
      stats << "#{summary['total_rows']} total rows"
      stats << "#{summary['total_columns']} maximum columns"
    when 'csv'
      summary = raw_data['summary']
      stats << "#{summary['row_count']} rows"
      stats << "#{summary['column_count']} columns"
    when 'text'
      summary = raw_data['summary']
      stats << "#{summary['line_count']} lines"
      stats << "#{summary['word_count']} words"
      stats << "#{summary['character_count']} characters"
    when 'json'
      stats << "Structured JSON data"
    end
    
    stats
  end
end
