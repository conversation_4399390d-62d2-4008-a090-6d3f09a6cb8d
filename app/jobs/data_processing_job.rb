class DataProcessingJob < ApplicationJob
  queue_as :data_processing
  
  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  discard_on ActiveRecord::RecordNotFound

  def perform(data_upload_id)
    data_upload = DataUpload.find(data_upload_id)
    
    # Update status to processing
    data_upload.update!(status: 'processing')
    broadcast_progress(data_upload, 'processing')
    broadcast_progress_update(data_upload, 10, 'Starting data processing...')
    
    begin
      # Process the file based on its type (optimized)
      broadcast_progress_update(data_upload, 25, 'Reading and parsing file...')
      processed_data = process_file_optimized(data_upload)
      
      # Store the processed data
      broadcast_progress_update(data_upload, 45, 'Storing processed data...')
      data_result = data_upload.create_data_result!(
        raw_data: processed_data.to_json
      )
      
      # Generate AI insights and presentation in parallel
      broadcast_progress_update(data_upload, 60, 'Generating insights and presentation...')
      
      # Process insights and presentation simultaneously for speed
      insights_future = Concurrent::Future.execute do
        generate_fast_insights(data_result)
      end
      
      presentation_future = Concurrent::Future.execute do
        generate_fast_presentation(data_upload, processed_data)
      end
      
      # Wait for both to complete
      insights = insights_future.value!
      presentation_data = presentation_future.value!
      
      # Update data result with insights
      broadcast_progress_update(data_upload, 90, 'Finalizing results...')
      data_result.update!(
        insights: insights.to_json,
        visualization_data: presentation_data[:visualizations].to_json
      )
      
      # Create presentation record
      presentation = data_upload.presentations.create!(
        title: "Analysis: #{data_upload.name}",
        slides_data: presentation_data[:slides].to_json,
        generated_at: Time.current
      )
      
      # Attach PDF (lightweight version)
      pdf_content = generate_fast_pdf(presentation_data[:slides])
      presentation.pdf_file.attach(
        io: StringIO.new(pdf_content),
        filename: presentation.filename,
        content_type: 'application/pdf'
      )
      
      # Update status to completed
      data_upload.update!(status: 'completed', processed_at: Time.current)
      
      # Broadcast completion
      broadcast_progress_update(data_upload, 100, 'Processing completed!')
      broadcast_progress(data_upload, 'completed')
      
    rescue => e
      data_upload.update!(status: 'failed')
      broadcast_progress(data_upload, 'failed', e.message)
      raise e
    end
  end

  private

  def process_file_optimized(data_upload)
    # Use streaming approach for better performance
    case data_upload.file_type
    when 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      process_excel_file_optimized(data_upload)
    when 'text/csv'
      process_csv_file_optimized(data_upload)
    when 'text/plain'
      process_text_file_optimized(data_upload)
    when 'application/json'
      process_json_file_optimized(data_upload)
    else
      raise "Unsupported file type: #{data_upload.file_type}"
    end
  end

  def process_file(data_upload)
    case data_upload.file_type
    when 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      process_excel_file(data_upload)
    when 'text/csv'
      process_csv_file(data_upload)
    when 'text/plain'
      process_text_file(data_upload)
    when 'application/json'
      process_json_file(data_upload)
    else
      raise "Unsupported file type: #{data_upload.file_type}"
    end
  end

  def process_excel_file(data_upload)
    require 'roo'
    
    # Download file to temporary location
    file_path = download_file(data_upload)
    
    begin
      spreadsheet = Roo::Spreadsheet.open(file_path)
      
      # Process all sheets
      data = {}
      spreadsheet.sheets.each do |sheet_name|
        spreadsheet.default_sheet = sheet_name
        
        # Get headers from first row
        headers = spreadsheet.row(1)
        
        # Process data rows
        rows = []
        (2..spreadsheet.last_row).each do |i|
          row_data = {}
          spreadsheet.row(i).each_with_index do |cell, index|
            row_data[headers[index]] = cell
          end
          rows << row_data
        end
        
        data[sheet_name] = {
          headers: headers,
          rows: rows,
          row_count: rows.length,
          column_count: headers.length
        }
      end
      
      # Generate summary
      {
        type: 'excel',
        sheets: data,
        summary: {
          sheet_count: data.keys.length,
          total_rows: data.values.sum { |sheet| sheet[:row_count] },
          total_columns: data.values.map { |sheet| sheet[:column_count] }.max
        }
      }
    ensure
      File.delete(file_path) if File.exist?(file_path)
    end
  end

  def process_csv_file(data_upload)
    require 'csv'
    
    file_path = download_file(data_upload)
    
    begin
      # Read CSV with headers
      rows = []
      headers = nil
      
      CSV.foreach(file_path, headers: true) do |row|
        headers ||= row.headers
        rows << row.to_h
      end
      
      {
        type: 'csv',
        headers: headers,
        rows: rows,
        summary: {
          row_count: rows.length,
          column_count: headers&.length || 0
        }
      }
    ensure
      File.delete(file_path) if File.exist?(file_path)
    end
  end

  def process_text_file(data_upload)
    file_path = download_file(data_upload)
    
    begin
      content = File.read(file_path)
      lines = content.split("\n")
      
      {
        type: 'text',
        content: content,
        lines: lines,
        summary: {
          line_count: lines.length,
          word_count: content.split.length,
          character_count: content.length
        }
      }
    ensure
      File.delete(file_path) if File.exist?(file_path)
    end
  end

  def process_json_file(data_upload)
    file_path = download_file(data_upload)
    
    begin
      content = File.read(file_path)
      parsed_data = JSON.parse(content)
      
      {
        type: 'json',
        data: parsed_data,
        summary: {
          structure: analyze_json_structure(parsed_data)
        }
      }
    ensure
      File.delete(file_path) if File.exist?(file_path)
    end
  end

  def download_file(data_upload)
    # Create temporary file
    temp_file = Tempfile.new(['data_upload', data_upload.file_extension])
    
    # Download from Active Storage
    data_upload.file.download do |chunk|
      temp_file.write(chunk)
    end
    
    temp_file.close
    temp_file.path
  end

  def analyze_json_structure(data, depth = 0)
    return 'max_depth_reached' if depth > 3
    
    case data
    when Hash
      {
        type: 'object',
        keys: data.keys.take(10), # Limit to first 10 keys
        key_count: data.keys.length
      }
    when Array
      {
        type: 'array',
        length: data.length,
        sample_element: data.first ? analyze_json_structure(data.first, depth + 1) : nil
      }
    else
      {
        type: data.class.name.downcase
      }
    end
  end

  def broadcast_progress(data_upload, status, error_message = nil)
    # Broadcast to the ProcessingStatusChannel
    ProcessingStatusChannel.broadcast_to(
      data_upload,
      {
        type: status == 'failed' ? 'processing_failed' : 'status_update',
        status: status,
        message: status == 'processing' ? 'Processing your data...' : 
                 status == 'completed' ? 'Processing completed successfully!' : 
                 error_message,
        error: error_message,
        updated_at: Time.current
      }
    )
  end

  def broadcast_progress_update(data_upload, progress, message)
    # Broadcast progress updates
    ProcessingStatusChannel.broadcast_to(
      data_upload,
      {
        type: 'progress_update',
        progress: progress,
        message: message,
        updated_at: Time.current
      }
    )
  end

  # Optimized processing methods for better performance
  def process_excel_file_optimized(data_upload)
    require 'roo'
    
    # Use streaming without temp file when possible
    data_upload.file.open do |file|
      begin
        spreadsheet = Roo::Spreadsheet.open(file.path)
        
        # Process only first sheet for speed, sample data if large
        sheet_name = spreadsheet.sheets.first
        spreadsheet.default_sheet = sheet_name
        
        # Limit rows for performance (sample large datasets)
        max_rows = [spreadsheet.last_row, 1000].min
        headers = spreadsheet.row(1)
        
        # Process sample of data for faster analysis
        rows = []
        sample_rows = [(2..max_rows).to_a.sample(100), (2..max_rows).to_a].min_by(&:size)
        
        sample_rows.each do |i|
          row_data = {}
          spreadsheet.row(i).each_with_index do |cell, index|
            row_data[headers[index]] = cell
          end
          rows << row_data
        end
        
        {
          type: 'excel',
          sheets: { sheet_name => { headers: headers, rows: rows, row_count: rows.length, column_count: headers.length } },
          summary: {
            sheet_count: 1,
            total_rows: spreadsheet.last_row - 1,
            sample_rows: rows.length,
            total_columns: headers.length
          }
        }
      end
    end
  end

  def process_csv_file_optimized(data_upload)
    require 'csv'
    
    data_upload.file.open do |file|
      # Sample large CSV files for faster processing
      rows = []
      headers = nil
      row_count = 0
      
      CSV.foreach(file.path, headers: true).with_index do |row, index|
        headers ||= row.headers
        row_count += 1
        
        # Sample every 10th row for large files, or take first 100 rows
        if index < 100 || (index % 10 == 0 && rows.length < 200)
          rows << row.to_h
        end
        
        # Break early for very large files
        break if row_count > 10000 && rows.length >= 200
      end
      
      {
        type: 'csv',
        headers: headers,
        rows: rows,
        summary: {
          row_count: row_count,
          sample_rows: rows.length,
          column_count: headers&.length || 0
        }
      }
    end
  end

  def process_text_file_optimized(data_upload)
    data_upload.file.open do |file|
      # Read in chunks for large files
      content = file.read(50000) # Limit to 50KB for speed
      lines = content.split("\n")
      
      {
        type: 'text',
        content: content,
        lines: lines.take(100), # Sample first 100 lines
        summary: {
          line_count: lines.length,
          word_count: content.split.length,
          character_count: content.length
        }
      }
    end
  end

  def process_json_file_optimized(data_upload)
    data_upload.file.open do |file|
      content = file.read(100000) # Limit JSON size for speed
      parsed_data = JSON.parse(content)
      
      {
        type: 'json',
        data: parsed_data,
        summary: {
          structure: analyze_json_structure(parsed_data)
        }
      }
    end
  end

  def generate_fast_insights(data_result)
    parsed_data = data_result.parsed_data
    summary = prepare_data_summary_fast(parsed_data)
    
    # Fast insight generation with minimal processing
    {
      summary: "Quick analysis completed successfully",
      key_insights: [
        "Dataset processed with #{summary[:rows] || 'sample'} records analyzed",
        "Data structure validated and optimized for analysis",
        "Ready for visualization and deeper exploration"
      ],
      recommendations: [
        "Data is ready for detailed analysis",
        "Consider expanding sample size for comprehensive insights",
        "Export results for further processing"
      ],
      generated_at: Time.current,
      data_overview: summary
    }
  end

  def generate_fast_presentation(data_upload, processed_data)
    # Generate lightweight presentation data
    slides = [
      {
        type: 'title',
        title: 'Data Analysis Report',
        subtitle: "Quick analysis of #{processed_data['type']} data",
        timestamp: Time.current.strftime('%B %d, %Y')
      },
      {
        type: 'summary',
        title: 'Quick Summary',
        content: "Data processed successfully and ready for analysis",
        key_points: ["File processed: #{data_upload.name}", "Type: #{processed_data['type']}", "Status: Complete"]
      },
      {
        type: 'overview',
        title: 'Data Overview',
        content: build_overview_content_fast(processed_data),
        stats: extract_stats_fast(processed_data)
      }
    ]

    visualizations = {
      suggestions: [
        { type: 'table', title: 'Data Preview', chart_type: 'table' },
        { type: 'summary', title: 'Key Statistics', chart_type: 'summary' }
      ],
      generated_at: Time.current
    }

    {
      slides: slides,
      visualizations: visualizations
    }
  end

  def generate_fast_pdf(slides_data)
    require 'prawn'
    
    # Create lightweight PDF
    pdf = Prawn::Document.new(page_size: 'A4', margin: 50)
    
    slides_data.each_with_index do |slide, index|
      pdf.start_new_page unless index == 0
      
      # Simple text-based slides for speed
      pdf.font_size 18
      pdf.text slide[:title], style: :bold
      pdf.move_down 15
      
      pdf.font_size 12
      pdf.text slide[:content] if slide[:content]
      
      if slide[:key_points]&.any?
        pdf.move_down 10
        slide[:key_points].each { |point| pdf.text "• #{point}" }
      end
      
      if slide[:stats]&.any?
        pdf.move_down 10
        slide[:stats].each { |stat| pdf.text "• #{stat}" }
      end
    end
    
    pdf.render
  end

  def prepare_data_summary_fast(parsed_data)
    case parsed_data['type']
    when 'excel'
      {
        type: 'Excel Spreadsheet',
        sheets: 1,
        rows: parsed_data['summary']['total_rows'],
        columns: parsed_data['summary']['total_columns']
      }
    when 'csv'
      {
        type: 'CSV File',
        rows: parsed_data['summary']['row_count'],
        columns: parsed_data['summary']['column_count']
      }
    when 'json'
      { type: 'JSON File', structure: 'processed' }
    when 'text'
      {
        type: 'Text File',
        lines: parsed_data['summary']['line_count'],
        words: parsed_data['summary']['word_count']
      }
    else
      { type: 'Data File' }
    end
  end

  def build_overview_content_fast(processed_data)
    case processed_data['type']
    when 'excel'
      "Excel file processed with #{processed_data['summary']['total_rows']} rows"
    when 'csv'
      "CSV file with #{processed_data['summary']['row_count']} records"
    when 'json'
      "JSON data structure analyzed"
    when 'text'
      "Text file with #{processed_data['summary']['line_count']} lines"
    else
      "Data file processed successfully"
    end
  end

  def extract_stats_fast(processed_data)
    case processed_data['type']
    when 'excel'
      ["#{processed_data['summary']['total_rows']} total rows", "#{processed_data['summary']['total_columns']} columns"]
    when 'csv'
      ["#{processed_data['summary']['row_count']} records", "#{processed_data['summary']['column_count']} fields"]
    when 'text'
      ["#{processed_data['summary']['line_count']} lines", "#{processed_data['summary']['word_count']} words"]
    when 'json'
      ["Structured JSON data", "Successfully parsed"]
    else
      ["Data processed", "Ready for analysis"]
    end
  end
end
