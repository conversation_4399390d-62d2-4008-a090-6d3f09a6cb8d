class DataProcessingJob < ApplicationJob
  queue_as :data_processing
  
  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  discard_on ActiveRecord::RecordNotFound

  def perform(data_upload_id)
    data_upload = DataUpload.find(data_upload_id)
    
    # Update status to processing
    data_upload.update!(status: 'processing')
    
    begin
      # Process the file based on its type
      processed_data = process_file(data_upload)
      
      # Store the processed data
      data_result = data_upload.create_data_result!(
        raw_data: processed_data.to_json
      )
      
      # Generate AI insights
      AiInsightGenerationJob.perform_later(data_result.id)
      
      # Update status to completed
      data_upload.update!(status: 'completed', processed_at: Time.current)
      
      # Broadcast update to user via Solid Cable
      broadcast_progress(data_upload, 'completed')
      
    rescue => e
      data_upload.update!(status: 'failed')
      broadcast_progress(data_upload, 'failed', e.message)
      raise e
    end
  end

  private

  def process_file(data_upload)
    case data_upload.file_type
    when 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      process_excel_file(data_upload)
    when 'text/csv'
      process_csv_file(data_upload)
    when 'text/plain'
      process_text_file(data_upload)
    when 'application/json'
      process_json_file(data_upload)
    else
      raise "Unsupported file type: #{data_upload.file_type}"
    end
  end

  def process_excel_file(data_upload)
    require 'roo'
    
    # Download file to temporary location
    file_path = download_file(data_upload)
    
    begin
      spreadsheet = Roo::Spreadsheet.open(file_path)
      
      # Process all sheets
      data = {}
      spreadsheet.sheets.each do |sheet_name|
        spreadsheet.default_sheet = sheet_name
        
        # Get headers from first row
        headers = spreadsheet.row(1)
        
        # Process data rows
        rows = []
        (2..spreadsheet.last_row).each do |i|
          row_data = {}
          spreadsheet.row(i).each_with_index do |cell, index|
            row_data[headers[index]] = cell
          end
          rows << row_data
        end
        
        data[sheet_name] = {
          headers: headers,
          rows: rows,
          row_count: rows.length,
          column_count: headers.length
        }
      end
      
      # Generate summary
      {
        type: 'excel',
        sheets: data,
        summary: {
          sheet_count: data.keys.length,
          total_rows: data.values.sum { |sheet| sheet[:row_count] },
          total_columns: data.values.map { |sheet| sheet[:column_count] }.max
        }
      }
    ensure
      File.delete(file_path) if File.exist?(file_path)
    end
  end

  def process_csv_file(data_upload)
    require 'csv'
    
    file_path = download_file(data_upload)
    
    begin
      # Read CSV with headers
      rows = []
      headers = nil
      
      CSV.foreach(file_path, headers: true) do |row|
        headers ||= row.headers
        rows << row.to_h
      end
      
      {
        type: 'csv',
        headers: headers,
        rows: rows,
        summary: {
          row_count: rows.length,
          column_count: headers&.length || 0
        }
      }
    ensure
      File.delete(file_path) if File.exist?(file_path)
    end
  end

  def process_text_file(data_upload)
    file_path = download_file(data_upload)
    
    begin
      content = File.read(file_path)
      lines = content.split("\n")
      
      {
        type: 'text',
        content: content,
        lines: lines,
        summary: {
          line_count: lines.length,
          word_count: content.split.length,
          character_count: content.length
        }
      }
    ensure
      File.delete(file_path) if File.exist?(file_path)
    end
  end

  def process_json_file(data_upload)
    file_path = download_file(data_upload)
    
    begin
      content = File.read(file_path)
      parsed_data = JSON.parse(content)
      
      {
        type: 'json',
        data: parsed_data,
        summary: {
          structure: analyze_json_structure(parsed_data)
        }
      }
    ensure
      File.delete(file_path) if File.exist?(file_path)
    end
  end

  def download_file(data_upload)
    # Create temporary file
    temp_file = Tempfile.new(['data_upload', data_upload.file_extension])
    
    # Download from Active Storage
    data_upload.file.download do |chunk|
      temp_file.write(chunk)
    end
    
    temp_file.close
    temp_file.path
  end

  def analyze_json_structure(data, depth = 0)
    return 'max_depth_reached' if depth > 3
    
    case data
    when Hash
      {
        type: 'object',
        keys: data.keys.take(10), # Limit to first 10 keys
        key_count: data.keys.length
      }
    when Array
      {
        type: 'array',
        length: data.length,
        sample_element: data.first ? analyze_json_structure(data.first, depth + 1) : nil
      }
    else
      {
        type: data.class.name.downcase
      }
    end
  end

  def broadcast_progress(data_upload, status, error_message = nil)
    ActionCable.server.broadcast(
      "data_upload_#{data_upload.id}",
      {
        type: 'processing_update',
        status: status,
        error_message: error_message,
        updated_at: Time.current
      }
    )
  end
end
