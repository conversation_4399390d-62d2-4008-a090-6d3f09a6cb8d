class AiInsightGenerationJob < ApplicationJob
  queue_as :ai_processing
  
  retry_on StandardError, wait: :exponentially_longer, attempts: 2
  discard_on ActiveRecord::RecordNotFound

  def perform(data_result_id)
    data_result = DataResult.find(data_result_id)
    data_upload = data_result.data_upload
    
    begin
      # Generate insights using AI
      broadcast_progress_update(data_upload, 85, 'Analyzing data patterns...')
      insights = generate_insights(data_result)
      
      # Generate visualization suggestions
      broadcast_progress_update(data_upload, 90, 'Creating visualization suggestions...')
      visualizations = suggest_visualizations(data_result)
      
      # Update the data result with insights
      data_result.update!(
        insights: insights.to_json,
        visualization_data: visualizations.to_json
      )
      
      # Generate presentation automatically
      broadcast_progress_update(data_upload, 95, 'Generating presentation...')
      PresentationGenerationJob.perform_later(data_upload.id)
      
    rescue => e
      Rails.logger.error "AI Insight Generation failed for DataResult #{data_result_id}: #{e.message}"
      # Broadcast failure
      ProcessingStatusChannel.broadcast_to(
        data_upload,
        {
          type: 'processing_failed',
          error: "AI insight generation failed: #{e.message}",
          updated_at: Time.current
        }
      )
      raise e
    end
  end

  private

  def broadcast_progress_update(data_upload, progress, message)
    ProcessingStatusChannel.broadcast_to(
      data_upload,
      {
        type: 'progress_update',
        progress: progress,
        message: message,
        updated_at: Time.current
      }
    )
  end

  private

  def generate_insights(data_result)
    parsed_data = data_result.parsed_data
    
    # Prepare data summary for AI analysis
    summary = prepare_data_summary(parsed_data)
    
    # For now, generate mock insights (replace with actual OpenAI integration)
    {
      summary: "Data analysis reveals interesting patterns and trends",
      key_insights: [
        "Dataset contains #{summary[:rows] || 'multiple'} records",
        "Data quality appears good with minimal missing values",
        "Several correlations detected between variables"
      ],
      recommendations: [
        "Consider deeper analysis of key trends",
        "Implement data validation processes",
        "Monitor data quality over time"
      ],
      generated_at: Time.current,
      data_overview: summary
    }
  end

  def suggest_visualizations(data_result)
    parsed_data = data_result.parsed_data
    
    visualizations = []
    
    case parsed_data['type']
    when 'excel', 'csv'
      visualizations << {
        type: 'bar',
        title: 'Data Overview',
        chart_type: 'bar'
      }
      
      visualizations << {
        type: 'line',
        title: 'Trend Analysis',
        chart_type: 'line'
      }
      
    when 'json'
      visualizations << {
        type: 'table',
        title: 'JSON Data Overview',
        chart_type: 'table'
      }
      
    when 'text'
      visualizations << {
        type: 'word_cloud',
        title: 'Word Frequency Analysis',
        chart_type: 'word_cloud'
      }
    end
    
    {
      suggestions: visualizations,
      generated_at: Time.current
    }
  end

  def prepare_data_summary(parsed_data)
    case parsed_data['type']
    when 'excel'
      {
        type: 'Excel Spreadsheet',
        sheets: parsed_data['summary']['sheet_count'],
        rows: parsed_data['summary']['total_rows'],
        columns: parsed_data['summary']['total_columns']
      }
    when 'csv'
      {
        type: 'CSV File',
        rows: parsed_data['summary']['row_count'],
        columns: parsed_data['summary']['column_count']
      }
    when 'json'
      {
        type: 'JSON File',
        structure: parsed_data['summary']['structure']
      }
    when 'text'
      {
        type: 'Text File',
        lines: parsed_data['summary']['line_count'],
        words: parsed_data['summary']['word_count']
      }
    else
      { type: 'Unknown' }
    end
  end
end
